package me.zivush.smp.gui;

import me.loving11ish.clans.api.models.Clan;
import me.zivush.smp.SMP;
import net.wesjd.anvilgui.AnvilGUI;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.configuration.ConfigurationSection;

import java.util.*;
import java.util.stream.Collectors;

public class SMPJoinGUI {
    private final SMP plugin;
    // Store page numbers separately for public and private sections
    private final Map<UUID, Integer> publicPageData;
    private final Map<UUID, Integer> privatePageData;
    private final Map<UUID, String> playerFilters;

    public SMPJoinGUI(SMP plugin) {
        this.plugin = plugin;
        this.publicPageData = new HashMap<>();
        this.privatePageData = new HashMap<>();
        this.playerFilters = new HashMap<>();
    }

    // --- SMPInfo Inner Class ---
    // Helper class to store SMP data for sorting and display
    private static class SMPInfo {
        final String smpName;
        final String displayName;
        final boolean isFull;
        final String privacy;
        final String difficulty;
        final int currentPlayers;
        final int maxPlayers;
        final ItemStack item; // Store the created itemstack directly

        SMPInfo(String smpName, String displayName, boolean isFull, String privacy, String difficulty, int currentPlayers, int maxPlayers, ItemStack item) {
            this.smpName = smpName;
            this.displayName = displayName;
            this.isFull = isFull;
            this.privacy = privacy;
            this.difficulty = difficulty;
            this.currentPlayers = currentPlayers;
            this.maxPlayers = maxPlayers;
            this.item = item;
        }

        public boolean isFull() { return isFull; }
        public String getDisplayName() { return displayName; }
        public ItemStack getItem() { return item; }
        public String getSmpName() { return smpName; }
    }
    // --- End SMPInfo Inner Class ---


    // Main method to open the GUI
    public void openGUI(Player player) {
        int publicPage = publicPageData.getOrDefault(player.getUniqueId(), 0);
        int privatePage = privatePageData.getOrDefault(player.getUniqueId(), 0);
        openGUI(player, publicPage, privatePage); // Call the internal method with current pages
    }

    // Method to open GUI with a specific filter
    public void openGUI(Player player, String filter) {
        playerFilters.put(player.getUniqueId(), filter);
        // Reset pages when applying a new filter
        publicPageData.put(player.getUniqueId(), 0);
        privatePageData.put(player.getUniqueId(), 0);
        openGUI(player);
    }

    // Internal method to open GUI for specific pages
    private void openGUI(Player player, int pagePublic, int pagePrivate) {
        ConfigurationSection guiConfig = plugin.getConfig().getConfigurationSection("gui.smp_join");
        Inventory inv = Bukkit.createInventory(null,
                guiConfig.getInt("size"),
                plugin.colorize(guiConfig.getString("title"))
        );

        // --- Basic GUI Setup (Fillers, Headers, Dividers) ---
        ItemStack filler = createUtilityItem(guiConfig.getConfigurationSection("filler"));
        for (int i = 0; i < inv.getSize(); i++) {
            inv.setItem(i, filler);
        }
        inv.setItem(guiConfig.getInt("private_section.title_item.slot"),
                createUtilityItem(guiConfig.getConfigurationSection("private_section.title_item")));
        inv.setItem(guiConfig.getInt("public_section.title_item.slot"),
                createUtilityItem(guiConfig.getConfigurationSection("public_section.title_item")));
        ItemStack divider = createUtilityItem(guiConfig.getConfigurationSection("divider"));
        for (int slot : guiConfig.getIntegerList("divider.slots")) {
            inv.setItem(slot, divider);
        }

        // Add search button
        inv.setItem(guiConfig.getInt("search.slot"),
                createUtilityItem(guiConfig.getConfigurationSection("search")));
        // --- End Basic GUI Setup ---


        // --- Fetch, Filter, and Sort SMP Data ---
        String filter = playerFilters.getOrDefault(player.getUniqueId(), "");
        List<SMPInfo> publicSmpInfos = getSortedSmps(player, true, filter); // Get sorted public SMPs
        List<SMPInfo> privateSmpInfos = getSortedSmps(player, false, filter); // Get sorted private SMPs
        // --- End Fetch, Filter, and Sort ---


        // --- Pagination Logic ---
        List<Integer> publicSlots = guiConfig.getIntegerList("public_section.title_item.slots");
        List<Integer> privateSlots = guiConfig.getIntegerList("private_section.title_item.slots");
        int maxPerPagePublic = publicSlots.size();
        int maxPerPagePrivate = privateSlots.size(); // Allow different sizes if needed

        int totalPublic = publicSmpInfos.size();
        int totalPrivate = privateSmpInfos.size();
        int totalPagesPublic = (totalPublic > 0) ? (totalPublic - 1) / maxPerPagePublic + 1 : 1; // At least 1 page
        int totalPagesPrivate = (totalPrivate > 0) ? (totalPrivate - 1) / maxPerPagePrivate + 1 : 1; // At least 1 page

        // Ensure pages are within valid bounds
        pagePublic = Math.max(0, Math.min(pagePublic, totalPagesPublic - 1));
        pagePrivate = Math.max(0, Math.min(pagePrivate, totalPagesPrivate - 1));

        // Update stored page numbers
        publicPageData.put(player.getUniqueId(), pagePublic);
        privatePageData.put(player.getUniqueId(), pagePrivate);
        // --- End Pagination Logic ---


        // --- Populate GUI with Sorted SMP Items for Current Page ---
        // Populate Public Section
        int startPublic = pagePublic * maxPerPagePublic;
        for (int i = 0; i < maxPerPagePublic && startPublic + i < totalPublic; i++) {
            inv.setItem(publicSlots.get(i), publicSmpInfos.get(startPublic + i).getItem()); // Get pre-made item from SMPInfo
        }

        // Populate Private Section
        int startPrivate = pagePrivate * maxPerPagePrivate;
        for (int i = 0; i < maxPerPagePrivate && startPrivate + i < totalPrivate; i++) {
            inv.setItem(privateSlots.get(i), privateSmpInfos.get(startPrivate + i).getItem()); // Get pre-made item from SMPInfo
        }
        // --- End Populate GUI ---


        // --- Set Navigation Arrows ---
        boolean hasNextPublic = (pagePublic + 1) * maxPerPagePublic < totalPublic;
        boolean hasNextPrivate = (pagePrivate + 1) * maxPerPagePrivate < totalPrivate;
        boolean hasPrevPublic = pagePublic > 0;
        boolean hasPrevPrivate = pagePrivate > 0;

        setPageArrows(inv, guiConfig, hasNextPublic, hasNextPrivate, hasPrevPublic, hasPrevPrivate);
        // --- End Set Navigation Arrows ---

        player.openInventory(inv);
    }

    // Helper method to get filtered and sorted SMP data
    private List<SMPInfo> getSortedSmps(Player player, boolean getPublic, String filter) {
        List<SMPInfo> smpInfos = new ArrayList<>();
        ConfigurationSection smps = plugin.getDatabase().getConfigurationSection("smps");
        if (smps == null) return smpInfos; // Return empty list if no SMPs exist

        String playerUUID = player.getUniqueId().toString();
        Clan playerClan = plugin.getClansAPI() != null ? plugin.getClansAPI().getClanByBukkitPlayer(player) : null;
        ConfigurationSection itemConfig = plugin.getConfig().getConfigurationSection("gui.smp_join.smp_item");

        for (String smpName : smps.getKeys(false)) {
            // Skip SMPs that are still loading
            boolean isLoading = smps.getBoolean(smpName + ".loading", false);
            if (isLoading) {
                continue; // Skip this SMP as it's still being created
            }

            // Gather data for the SMP
            String ownerUUID = smps.getString(smpName + ".owner");
            OfflinePlayer owner = Bukkit.getOfflinePlayer(UUID.fromString(ownerUUID)); // Owner can be offline
            String privacy = smps.getString(smpName + ".privacy");
            String difficulty = smps.getString(smpName + ".difficulty", "peaceful"); // Default if missing
            String displayName = smps.getString(smpName + ".display_name", smpName); // Use actual name if display name missing
            int currentPlayers = plugin.countSMPPlayers(smpName);
            int maxPlayers = plugin.getMaxPermissionValue(owner, "smp.maxplayers.", 10); // Default 10
            boolean isFull = currentPlayers >= maxPlayers;

            // Check if player should see this SMP based on requested section (public/private) and permissions/clan
            boolean shouldAdd = false;
            if (getPublic && privacy.equals("public")) {
                shouldAdd = true;
            } else if (!getPublic && privacy.equals("private")) {
                // Player owns it OR Player is in a clan and Owner is in the same clan
                if (ownerUUID.equals(playerUUID)) {
                    shouldAdd = true;
                } else if (plugin.getClansAPI() != null && playerClan != null) {
                    // Safely get owner's clan (handle offline owner)
                    Clan ownerClan = null;
                    Player ownerOnline = owner.getPlayer(); // Bukkit.getPlayer(owner.getUniqueId()); is safer if owner might not be cached
                    if (ownerOnline != null) {
                        // If owner is online, use getClanByBukkitPlayer
                        ownerClan = plugin.getClansAPI().getClanByBukkitPlayer(ownerOnline);
                    } else {
                        // If owner is offline, use getClanByBukkitOfflinePlayerOwner
                        try { // Add try-catch for safety if API method might throw exceptions for offline players
                            ownerClan = plugin.getClansAPI().getClanByBukkitOfflinePlayerOwner(owner);
                        } catch (Exception e) {
                            plugin.getLogger().warning("Could not get clan for offline owner: " + owner.getName() + " for SMP: " + smpName);
                        }
                    }

                    if (ownerClan != null && playerClan.equals(ownerClan)) {
                        shouldAdd = true;
                    }
                }
            }


            if (shouldAdd) {
                // Apply search filter
                if (filter != null && !filter.isEmpty()) {
                    if (!displayName.toLowerCase().contains(filter.toLowerCase()) &&
                        !smpName.toLowerCase().contains(filter.toLowerCase())) {
                        continue; // Skip this SMP if it doesn't match the filter
                    }
                }

                // Create the item stack *once* here
                ItemStack smpItem = createSMPItem(smpName, displayName, owner, currentPlayers, maxPlayers, privacy, difficulty, itemConfig);
                // Create the SMPInfo object
                SMPInfo info = new SMPInfo(smpName, displayName, isFull, privacy, difficulty, currentPlayers, maxPlayers, smpItem);
                smpInfos.add(info);
            }
        }

        // Define the comparator
        Comparator<SMPInfo> smpComparator = Comparator
                .comparing(SMPInfo::isFull) // Sorts false (not full) before true (full)
                .thenComparing(SMPInfo::getDisplayName, String.CASE_INSENSITIVE_ORDER); // Then sort by name

        // Sort the list
        smpInfos.sort(smpComparator);

        return smpInfos;
    }


    // Creates the ItemStack for a specific SMP
    private ItemStack createSMPItem(String smpName, String displayName, OfflinePlayer owner, int currentPlayers, int maxPlayers, String privacy, String difficulty, ConfigurationSection config) {
        Material material = Material.matchMaterial(config.getString("material", "PLAYER_HEAD")); // Default to player head
        if (material == null) material = Material.PLAYER_HEAD; // Fallback again
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        if (meta instanceof SkullMeta && owner != null) {
            SkullMeta skullMeta = (SkullMeta) meta;
            // Set owning player for the skull
            skullMeta.setOwningPlayer(owner); // Use the OfflinePlayer object directly
        }

        // Set display name
        String nameFormat = config.getString("name", "&b%name%"); // Default if missing
        meta.setDisplayName(plugin.colorize(nameFormat.replace("%name%", displayName))); // Use the already fetched display name

        // Set lore with replaced placeholders
        List<String> lore = config.getStringList("lore").stream()
                .map(line -> line
                        .replace("%current%", String.valueOf(currentPlayers))
                        .replace("%max%", String.valueOf(maxPlayers))
                        .replace("%privacy%", privacy)
                        .replace("%difficulty%", difficulty)
                )
                .map(plugin::colorize)
                .collect(Collectors.toList());
        meta.setLore(lore);

        item.setItemMeta(meta);
        return item;
    }

    // Creates utility items like fillers, headers, arrows
    private ItemStack createUtilityItem(ConfigurationSection config) {
        Material material = Material.matchMaterial(config.getString("material", "STONE")); // Default material
        if (material == null) material = Material.STONE; // Fallback
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName(plugin.colorize(config.getString("name", " "))); // Default to space

        if (config.contains("lore")) {
            meta.setLore(
                    config.getStringList("lore").stream()
                            .map(plugin::colorize)
                            .collect(Collectors.toList())
            );
        }
        // Add custom model data if present
        if (config.contains("custom_model_data") && config.getInt("custom_model_data") > 0) {
            meta.setCustomModelData(config.getInt("custom_model_data"));
        }

        item.setItemMeta(meta);
        return item;
    }

    // Sets the navigation arrow items based on whether next/previous pages exist
    private void setPageArrows(Inventory inv, ConfigurationSection guiConfig, boolean hasNextPublic, boolean hasNextPrivate, boolean hasPrevPublic, boolean hasPrevPrivate) {
        if (hasNextPublic) {
            inv.setItem(guiConfig.getInt("next_page.slots.public"),
                    createUtilityItem(guiConfig.getConfigurationSection("next_page")));
        }
        if (hasNextPrivate) {
            inv.setItem(guiConfig.getInt("next_page.slots.private"),
                    createUtilityItem(guiConfig.getConfigurationSection("next_page")));
        }
        if (hasPrevPublic) {
            inv.setItem(guiConfig.getInt("previous_page.slots.public"),
                    createUtilityItem(guiConfig.getConfigurationSection("previous_page")));
        }
        if (hasPrevPrivate) {
            inv.setItem(guiConfig.getInt("previous_page.slots.private"),
                    createUtilityItem(guiConfig.getConfigurationSection("previous_page")));
        }
    }

    // Handles clicks within the GUI
    public void handleClick(Player player, int slot, Inventory inventory) {
        ConfigurationSection guiConfig = plugin.getConfig().getConfigurationSection("gui.smp_join");
        int currentPagePublic = publicPageData.getOrDefault(player.getUniqueId(), 0);
        int currentPagePrivate = privatePageData.getOrDefault(player.getUniqueId(), 0);

        // --- Handle Navigation Clicks ---
        if (slot == guiConfig.getInt("next_page.slots.public")) {
            publicPageData.put(player.getUniqueId(), currentPagePublic + 1);
            openGUI(player); // Reopen with updated page
            return;
        }
        if (slot == guiConfig.getInt("next_page.slots.private")) {
            privatePageData.put(player.getUniqueId(), currentPagePrivate + 1);
            openGUI(player);
            return;
        }
        if (slot == guiConfig.getInt("previous_page.slots.public")) {
            if (currentPagePublic > 0) {
                publicPageData.put(player.getUniqueId(), currentPagePublic - 1);
                openGUI(player);
            }
            return;
        }
        if (slot == guiConfig.getInt("previous_page.slots.private")) {
            if (currentPagePrivate > 0) {
                privatePageData.put(player.getUniqueId(), currentPagePrivate - 1);
                openGUI(player);
            }
            return;
        }

        // Handle search button click
        if (slot == guiConfig.getInt("search.slot")) {
            openSearchAnvil(player);
            return;
        }
        // --- End Navigation Clicks ---

        // --- Handle SMP Item Click ---
        List<Integer> publicSlots = guiConfig.getIntegerList("public_section.title_item.slots");
        List<Integer> privateSlots = guiConfig.getIntegerList("private_section.title_item.slots");
        int maxPerPagePublic = publicSlots.size();
        int maxPerPagePrivate = privateSlots.size();

        String clickedSmpName = null;

        if (publicSlots.contains(slot)) {
            int indexInPage = publicSlots.indexOf(slot);
            int overallIndex = currentPagePublic * maxPerPagePublic + indexInPage;
            // Re-fetch and sort to ensure consistency
            String filter = playerFilters.getOrDefault(player.getUniqueId(), "");
            List<SMPInfo> sortedPublic = getSortedSmps(player, true, filter);
            if (overallIndex >= 0 && overallIndex < sortedPublic.size()) {
                clickedSmpName = sortedPublic.get(overallIndex).getSmpName();
            }
        } else if (privateSlots.contains(slot)) {
            int indexInPage = privateSlots.indexOf(slot);
            int overallIndex = currentPagePrivate * maxPerPagePrivate + indexInPage;
            // Re-fetch and sort to ensure consistency
            String filter = playerFilters.getOrDefault(player.getUniqueId(), "");
            List<SMPInfo> sortedPrivate = getSortedSmps(player, false, filter);
            if (overallIndex >= 0 && overallIndex < sortedPrivate.size()) {
                clickedSmpName = sortedPrivate.get(overallIndex).getSmpName();
            }
        }

        // If a valid SMP name was found for the clicked slot
        if (clickedSmpName != null) {
            player.closeInventory();
            // Use the retrieved actual smpName to join
            plugin.handleJoin(player, clickedSmpName);
        }
        // --- End SMP Item Click ---
    }

    private void openSearchAnvil(Player player) {
        new AnvilGUI.Builder()
                .onClick((slot, stateSnapshot) -> {
                    String search = stateSnapshot.getText().toLowerCase();
                    playerFilters.put(player.getUniqueId(), search);
                    // Reset pages when searching
                    publicPageData.put(player.getUniqueId(), 0);
                    privatePageData.put(player.getUniqueId(), 0);
                    openGUI(player);

                    return Arrays.asList(AnvilGUI.ResponseAction.close());
                })
                .text(plugin.getConfig().getString("gui.smp_join.search.text"))
                .title(plugin.colorize(plugin.getConfig().getString("gui.smp_join.search.title")))
                .plugin(plugin)
                .open(player);
    }
}