package me.zivush.smp;

import com.eternalcode.formatter.ChatFormatterApi;
import com.eternalcode.formatter.ChatFormatterApiProvider;
import com.google.common.io.ByteArrayDataInput;
import com.google.common.io.ByteArrayDataOutput;
import com.google.common.io.ByteStreams;
import com.onarandombox.MultiverseCore.MultiverseCore;
import com.onarandombox.MultiverseCore.api.MVWorldManager;
import me.loving11ish.clans.api.ClansLiteAPI;
import me.loving11ish.clans.api.models.Clan;
import me.zivush.smp.chat.SMPChatListener;
import me.zivush.smp.economy.MultiworldMoneyManager;
import me.zivush.smp.gui.*;
import me.zivush.smp.plugins.SMPPluginManager;
import net.wesjd.anvilgui.AnvilGUI;
import org.bukkit.*;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.BookMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.plugin.messaging.PluginMessageListener;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class SMP extends JavaPlugin implements Listener, PluginMessageListener {
    private FileConfiguration config;
    private File databaseFile;
    private FileConfiguration database;
    private MultiverseCore mvCore;
    private ClansLiteAPI clansAPI;
    private SMPCreatorGUI creatorGUI;
    private SMPSettingsGUI settingsGUI;
    private SMPJoinGUI joinGUI;
    private SMPAgreementBookGUI agreementBookGUI;
    private boolean isProcessingMVInv = false;
    private Queue<Runnable> mvInvQueue = new LinkedList<>();
    private Map<UUID, String> pendingNames = new HashMap<>();
    private ChatFormatterApi chatFormatterApi;
    private SMPPluginManager pluginManager;
    private SMPPluginGUI pluginGUI;
    private Map<UUID, Boolean> creationKeepInventory = new HashMap<>();
    private MultiworldMoneyManager multiworldMoneyManager;
    private Map<UUID, BossBar> immunityBars = new HashMap<>();
    private Map<UUID, Long> combatCooldownTimestamps = new HashMap<>();
    private Map<UUID, Long> smpJoinCooldownTimestamps = new HashMap<>();
    private Map<String, Map<UUID, Long>> commandCooldownTimestamps = new HashMap<>();

    // Plugin messaging channel for communication with Velocity
    public static final String PLUGIN_CHANNEL = "smp:channel";

    @Override
    public void onEnable() {
        saveDefaultConfig();
        config = getConfig();
        getServer().getPluginManager().registerEvents(this, this);
        this.creatorGUI = new SMPCreatorGUI(this);
        this.settingsGUI = new SMPSettingsGUI(this);
        this.joinGUI = new SMPJoinGUI(this);
        this.agreementBookGUI = new SMPAgreementBookGUI(this);
        this.pluginManager = new SMPPluginManager(this);
        this.pluginGUI = new SMPPluginGUI(this);
        this.pluginManager.loadPlugins();
        this.multiworldMoneyManager = new MultiworldMoneyManager(this);

        Plugin chatFormatter = getServer().getPluginManager().getPlugin("ChatFormatter");
        if (chatFormatter == null) {
            getLogger().severe("ChatFormatter plugin not found! Disabling plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        try {
            this.chatFormatterApi = ChatFormatterApiProvider.get();
        } catch (IllegalStateException e) {
            getLogger().severe("ChatFormatter API not found! Disabling plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        getServer().getPluginManager().registerEvents(new SMPChatListener(this), this);


        databaseFile = new File(getDataFolder(), "database.yml");
        if (!databaseFile.exists()) {
            saveResource("database.yml", false);
        }
        database = YamlConfiguration.loadConfiguration(databaseFile);

        if (database.getConfigurationSection("smps") == null) {
            database.createSection("smps");
            try {
                database.save(databaseFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        mvCore = (MultiverseCore) Bukkit.getPluginManager().getPlugin("Multiverse-Core");
        if (mvCore == null) {
            getLogger().severe("Multiverse-Core not found! Disabling plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        clansAPI = (ClansLiteAPI) Bukkit.getPluginManager().getPlugin("ClansLite").getServer().getServicesManager().getRegistration(ClansLiteAPI.class).getProvider();
        if (clansAPI == null) {
            getLogger().warning("ClansLite not found - clan features disabled");
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    String uuid = player.getUniqueId().toString();

                    // Check all SMPs owned by this player
                    for (String smpName : database.getConfigurationSection("smps.").getKeys(false)) {
                        if (database.getString("smps." + smpName + ".owner").equals(uuid)) {
                            int borderSize = getMaxPermissionValue(player, "smp.worldsize.", 1000) / 2;
                            String difficulty = database.getString("smps." + smpName + ".difficulty");

                            // Update border for all dimensions
                            String[] worldTypes = {"", "_nether", "_the_end"};
                            for (String type : worldTypes) {
                                World world = Bukkit.getWorld(generateWorldName(smpName, type));
                                if (world != null) {
                                    WorldBorder border = world.getWorldBorder();
                                    if (border.getSize() != borderSize * 2) {
                                        border.setCenter(0, 0);
                                        border.setSize(borderSize * 2);
                                    }
                                }
                                if (difficulty == null) {
                                    difficulty = "peaceful"; // Default to peaceful if not specified
                                }
                                if (world != null && !world.getDifficulty().name().toLowerCase().equals(difficulty)) {
                                    world.setDifficulty(Difficulty.valueOf(difficulty.toUpperCase()));
                                }
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(this, 20L, config.getInt("world_border.check_interval") * 20L);
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    ConfigurationSection smps = database.getConfigurationSection("smps");
                    if (smps == null) continue;

                    boolean isInClan = clansAPI != null && clansAPI.getClanByBukkitPlayer(player) != null;

                    for (String smpName : smps.getKeys(false)) {
                        String ownerUUID = smps.getString(smpName + ".owner");
                        String privacy = smps.getString(smpName + ".privacy");

                        if (ownerUUID.equals(player.getUniqueId().toString())
                                && privacy.equals("private")
                                && !isInClan) {

                            database.set("smps." + smpName + ".privacy", "public");
                        }
                    }

                    try {
                        database.save(databaseFile);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }.runTaskTimer(this, 1200L, 1200L); // 200 ticks = 10 seconds

        // Register PlaceholderAPI expansion if available
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            getLogger().info("Registering PlaceholderAPI expansion...");
            new me.zivush.smp.placeholders.SMPPlaceholderExpansion(this).register();
        }

        // Start inactivity checker if enabled
        if (config.getBoolean("inactivity.enabled", true)) {
            startInactivityChecker();
            // Also check on startup
            checkInactiveSMPs();
        }

        // Start average player count tracker
        startAveragePlayerTracker();

        // Start daily average player task
        startDailyAveragePlayerTask();

        // Register plugin messaging channels
        getServer().getMessenger().registerOutgoingPluginChannel(this, PLUGIN_CHANNEL);
        getServer().getMessenger().registerIncomingPluginChannel(this, PLUGIN_CHANNEL, this);
        getLogger().info("Registered plugin messaging channels for Velocity communication");

        // Disable achievement announcements for all existing SMP worlds
        disableAchievementAnnouncementsForExistingSMPs();
    }

    @Override
    public void onPluginMessageReceived(String channel, Player player, byte[] message) {
        if (!channel.equals(PLUGIN_CHANNEL)) {
            return;
        }

        ByteArrayDataInput in = ByteStreams.newDataInput(message);
        String subChannel = in.readUTF();

        switch (subChannel) {
            case "CreateSMP":
                // Format: CreateSMP|playerName|smpName|privacy|difficulty
                String playerName = in.readUTF();
                String smpName = in.readUTF();
                String privacy = in.readUTF();
                String difficulty = in.readUTF();

                // Execute the actual SMP creation
                handleCreateFromConsole(playerName, smpName, privacy, difficulty);
                break;

            case "Error":
                // Format: Error|playerName|errorType
                String errorPlayerName = in.readUTF();
                String errorType = in.readUTF();

                Player errorPlayer = Bukkit.getPlayerExact(errorPlayerName);
                if (errorPlayer != null && errorPlayer.isOnline()) {
                    if (errorType.equals("name_already_exists")) {
                        sendMessage(errorPlayer, "name_exists");
                    } else {
                        sendMessage(errorPlayer, "smp_creation_error");
                    }
                }
                break;
        }
    }

    public void openNameAnvilGUI(Player player, String defaultName, String action) {
        ItemStack playerHead = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta skullMeta = (SkullMeta) playerHead.getItemMeta();
        skullMeta.setOwningPlayer(player);
        playerHead.setItemMeta(skullMeta);

        new AnvilGUI.Builder()
                .onClick((slot, stateSnapshot) -> {
                    if(slot != AnvilGUI.Slot.OUTPUT) {
                        return Collections.emptyList();
                    }

                    String name = stateSnapshot.getText();

                    // Validate name
                    if(name.length() > 16) {
                        return Arrays.asList(
                                AnvilGUI.ResponseAction.replaceInputText(defaultName),
                                AnvilGUI.ResponseAction.updateTitle(colorize(getConfig().getString("messages.anvil_invalid_name")), true)
                        );
                    }
                    if(!name.matches("^[a-zA-Z0-9_]+$")) {
                        return Arrays.asList(
                                AnvilGUI.ResponseAction.replaceInputText(defaultName),
                                AnvilGUI.ResponseAction.updateTitle(colorize(getConfig().getString("messages.anvil_invalid_name")), true)
                        );
                    }

                    // Check if name exists
                    ConfigurationSection smps = database.getConfigurationSection("smps");
                    if(smps != null) {
                        for(String smpName : smps.getKeys(false)) {
                            if(smpName.equals(name) ||
                                    smps.getString(smpName + ".display_name", smpName).equals(name)) {
                                return Arrays.asList(
                                        AnvilGUI.ResponseAction.replaceInputText(defaultName),
                                        AnvilGUI.ResponseAction.updateTitle(colorize(getConfig().getString("messages.anvil_name_exists")), true)
                                );
                            }
                        }
                    }

                    return Arrays.asList(
                            AnvilGUI.ResponseAction.close(),
                            AnvilGUI.ResponseAction.run(() -> {
                                if(action.equals("create")) {
                                    SMPCreatorGUI.CreationData data = creatorGUI.getPlayerData(player);
                                    handleCreate(player, name,
                                            data.privacy == null ? "public" : data.privacy,
                                            data.difficulty == null ? "peaceful" : data.difficulty);
                                } else if(action.startsWith("rename:")) {
                                    String smpName = action.split(":")[1];
                                    handleDisplayNameChange(player, smpName, name);
                                    settingsGUI.openGUI(player, smpName);
                                }
                            })
                    );
                })
                .text(defaultName)
                .itemLeft(playerHead)
                .title(colorize(getConfig().getString("messages.anvil_title")))
                .plugin(this)
                .open(player);
    }


    public void requestSMPName(Player player, String action) {
        pendingNames.put(player.getUniqueId(), action);
        sendMessage(player, "enter_smp_name");
    }

    /**
     * Generates a world name in the format: SMP/<smp_name>/world[_nether|_the_end]
     * @param smpName The name of the SMP
     * @param worldType The type of world (empty string for overworld, "_nether" or "_the_end")
     * @return The formatted world name
     */
    public String generateWorldName(String smpName, String worldType) {
        return "SMP/" + smpName + "/world" + worldType;
    }

    /**
     * Checks if a world name belongs to an SMP (using the new naming convention)
     * @param worldName The world name to check
     * @return true if the world belongs to an SMP, false otherwise
     */
    public boolean isWorldInSMP(String worldName) {
        return worldName.startsWith("SMP/") &&
               (worldName.endsWith("/world") ||
                worldName.endsWith("/world_nether") ||
                worldName.endsWith("/world_the_end"));
    }

    /**
     * Extracts the SMP name from a world name
     * @param worldName The world name to extract from
     * @return The SMP name, or null if the world is not an SMP world
     */
    public String extractSMPName(String worldName) {
        if (!isWorldInSMP(worldName)) {
            return null;
        }

        // Format: SMP/<smp_name>/world[_nether|_the_end]
        String withoutPrefix = worldName.substring("SMP/".length());
        return withoutPrefix.substring(0, withoutPrefix.indexOf("/"));
    }

    private void updateTabVisibility(Player targetPlayer) {
        String targetWorld = targetPlayer.getWorld().getName();
        boolean targetInSMP = isWorldInSMP(targetWorld);
        String targetSMP = targetInSMP ? extractSMPName(targetWorld) : null;

        // No invisibility based on permissions
        boolean targetIsInvisible = false;

        for (Player other : Bukkit.getOnlinePlayers()) {
            if (other.equals(targetPlayer)) continue; // Skip self

            String otherWorld = other.getWorld().getName();
            boolean otherInSMP = isWorldInSMP(otherWorld);
            String otherSMP = otherInSMP ? extractSMPName(otherWorld) : null;

            // No invisibility based on permissions
            boolean otherIsInvisible = false;

            // --- Start Visibility Logic ---
            boolean shouldSeeEachOther = false;

            if (targetInSMP && otherInSMP) {
                // Both in SMPs - show only if same SMP
                if (targetSMP.equals(otherSMP)) {
                    shouldSeeEachOther = true;
                } else {
                    shouldSeeEachOther = false;
                }
            } else if (!targetInSMP && !otherInSMP) {
                // Neither in SMP - show each other
                shouldSeeEachOther = true;
            } else {
                // One in SMP, one not - hide from each other
                shouldSeeEachOther = false;
            }

            // Apply visibility settings
            if (shouldSeeEachOther) {
                targetPlayer.showPlayer(this, other);
                other.showPlayer(this, targetPlayer);
            } else {
                targetPlayer.hidePlayer(this, other);
                other.hidePlayer(this, targetPlayer);
            }
            // --- End Visibility Logic ---
        }
    }

    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onCrossSmpCommandCheck(PlayerCommandPreprocessEvent event) {
        Player sender = event.getPlayer();
        String senderWorldName = sender.getWorld().getName();

        // 1. Check if feature is enabled and sender is in an SMP
        if (!config.getBoolean("cross_smp_restrictions.enabled", true) || !isWorldInSMP(senderWorldName)) {
            return;
        }

        // 2. Check for bypass permission or OP status
        if (sender.isOp() || sender.hasPermission(config.getString("cross_smp_restrictions.bypass_permission", "smp.bypass.crosscommand"))) {
            return;
        }

        String senderSmpName = extractSMPName(senderWorldName);

        // 3. Parse command and arguments
        String message = event.getMessage().substring(1); // Command without '/'
        String[] parts = message.split(" ");
        if (parts.length == 0) return; // Empty command?

        String commandRoot = parts[0].toLowerCase();
        String[] args = new String[parts.length - 1];
        System.arraycopy(parts, 1, args, 0, parts.length - 1);

        // 4. Check if the command is in the restricted list
        ConfigurationSection restrictedCommands = config.getConfigurationSection("cross_smp_restrictions.restricted_commands");
        if (restrictedCommands == null || !restrictedCommands.contains(commandRoot)) {
            return;
        }

        int playerArgIndex = restrictedCommands.getInt(commandRoot, -1);
        if (playerArgIndex == -1 || args.length <= playerArgIndex) {
            // Command not configured correctly or not enough args for the check
            return;
        }

        // 5. Find the target player
        String targetPlayerName = args[playerArgIndex];
        Player targetPlayer = Bukkit.getPlayer(targetPlayerName);

        // 6. If target is offline, allow the command (can't check their world)
        if (targetPlayer == null || !targetPlayer.isOnline()) {
            return;
        }

        // 7. Check target player's world
        String targetWorldName = targetPlayer.getWorld().getName();
        boolean targetInAnySmp = isWorldInSMP(targetWorldName);

        // 8. Apply restriction logic
        if (targetInAnySmp) {
            // Target is in an SMP, check if it's the *same* SMP
            String targetSmpName = extractSMPName(targetWorldName);
            if (!senderSmpName.equals(targetSmpName)) {
                // Target is in a DIFFERENT SMP - Block!
                event.setCancelled(true);
                sendMessage(sender, config.getString("cross_smp_restrictions.blocked_message_key", "cross_smp_command_blocked"));
            }
            // If names match, it's allowed (implicitly falls through)
        } else {
            // Target is NOT in any SMP world - Block!
            event.setCancelled(true);
            sendMessage(sender, config.getString("cross_smp_restrictions.blocked_message_key", "cross_smp_command_blocked"));
        }
    }
    @EventHandler
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();
        String deathWorld = player.getWorld().getName();

        // Check if death occurred in an SMP world
        if(isWorldInSMP(deathWorld)) {
            String smpName = extractSMPName(deathWorld);
            boolean isInCreationSafety = creationKeepInventory.containsKey(player.getUniqueId());

            if (isInCreationSafety) {
                // Player is in creation safety mode - teleport to a safe location near death
                Location deathLoc = player.getLastDeathLocation();
                if (deathLoc != null && deathLoc.getWorld().getName().equals(deathWorld)) {
                    // Find a safe location near the death point
                    Location safeLoc = findSafeLocationNearDeath(deathLoc);

                    // Set the respawn location to the safe location
                    if (safeLoc != null) {
                        getLogger().info("Creation safety respawn: Teleporting " + player.getName() + " to safe location after death: " +
                                safeLoc.getWorld().getName() + " at " + safeLoc.getX() + ", " + safeLoc.getY() + ", " + safeLoc.getZ());
                        event.setRespawnLocation(safeLoc);
                    }
                }
            } else {
                // Regular player - let them respawn at their bed or SMP spawn
                // The default Minecraft behavior will handle this correctly
                // We just log the respawn location for debugging
                Location respawnLoc = event.getRespawnLocation();
                getLogger().info("Regular respawn: Player " + player.getName() + " respawning at: " +
                        respawnLoc.getWorld().getName() + " at " + respawnLoc.getX() + ", " +
                        respawnLoc.getY() + ", " + respawnLoc.getZ());
            }

            // Save the respawn location to database
            Location respawnLoc = event.getRespawnLocation();
            database.set("locations." + player.getUniqueId() + "." + smpName + ".world", respawnLoc.getWorld().getName());
            database.set("locations." + player.getUniqueId() + "." + smpName + ".x", respawnLoc.getX());
            database.set("locations." + player.getUniqueId() + "." + smpName + ".y", respawnLoc.getY());
            database.set("locations." + player.getUniqueId() + "." + smpName + ".z", respawnLoc.getZ());
            database.set("locations." + player.getUniqueId() + "." + smpName + ".yaw", respawnLoc.getYaw());
            database.set("locations." + player.getUniqueId() + "." + smpName + ".pitch", respawnLoc.getPitch());

            try {
                database.save(databaseFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true) // Monitor: run after other plugins, ignoreCancelled: don't trigger if damage was cancelled
    public void onPlayerDamageByEntity(EntityDamageByEntityEvent event) {
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            // Check if the player is in an SMP world
            if (isWorldInSMP(player.getWorld().getName())) {
                // Record the time of the damage
                combatCooldownTimestamps.put(player.getUniqueId(), System.currentTimeMillis());
                debugLog("Recorded combat timestamp for player " + player.getName() +
                        " with cooldown of " + config.getLong("combat_join_cooldown", 10) + " seconds");

                // Log the damager information
                if (event.getDamager() instanceof Player) {
                    Player damager = (Player) event.getDamager();
                    debugLog("Player " + player.getName() + " was damaged by player " + damager.getName() +
                            " for " + event.getDamage() + " damage");
                } else {
                    debugLog("Player " + player.getName() + " was damaged by " + event.getDamager().getType().name() +
                            " for " + event.getDamage() + " damage");
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.LOWEST)
    public void onSpawnCommand(PlayerCommandPreprocessEvent event) {
        Player player = event.getPlayer();
        String worldName = player.getWorld().getName();
        String commandRaw = event.getMessage().substring(1); // Get command without '/'
        String commandRoot = commandRaw.split(" ")[0].toLowerCase(); // Get the base command

        // Check if player is in an SMP world
        if (isWorldInSMP(worldName)) {

            // --- Start Spawn Cooldown Check ---
            if (commandRoot.equalsIgnoreCase("spawn")) { // Check specifically for /spawn
                long cooldownSeconds = config.getLong("combat_spawn_cooldown", 15); // Get cooldown from config, default 15
                if (cooldownSeconds > 0 && combatCooldownTimestamps.containsKey(player.getUniqueId())) {
                    long lastDamageTime = combatCooldownTimestamps.get(player.getUniqueId());
                    long cooldownMillis = cooldownSeconds * 1000;
                    long cooldownEndTime = lastDamageTime + cooldownMillis;
                    long currentTime = System.currentTimeMillis();

                    if (currentTime < cooldownEndTime) {
                        // Still in cooldown
                        long remainingMillis = cooldownEndTime - currentTime;
                        sendMessage(player, "spawn_cooldown_active", "%time%", formatTime(remainingMillis));
                        event.setCancelled(true);
                        return; // Stop further processing for this command
                    } else {
                        // Cooldown expired, remove timestamp
                        combatCooldownTimestamps.remove(player.getUniqueId());
                    }
                }
            }
        }
    }
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        updateTabVisibility(event.getPlayer());
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        updateTabVisibility(event.getPlayer());
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();

        String title = event.getView().getTitle();
        String creatorTitle = colorize(getConfig().getString("gui.smp_creator.title"));
        String settingsTitle = colorize(getConfig().getString("gui.smp_settings.title").replace("%name%", ".*"));
        String joinTitle = colorize(getConfig().getString("gui.smp_join.title"));
        String pluginTitle = colorize(getConfig().getString("gui.smp_plugins.title"));
        String accessTitle = colorize(getConfig().getString("gui.plugin_access.title"));


        if (title.equals(creatorTitle)) {
            event.setCancelled(true);
            creatorGUI.handleClick(player, event.getRawSlot());
        } else if (title.matches(settingsTitle)) {
            event.setCancelled(true);
            settingsGUI.handleClick(player, event.getRawSlot(), event.getInventory());
        } else if (title.equals(joinTitle)) {
            event.setCancelled(true);
            joinGUI.handleClick(player, event.getRawSlot(), event.getInventory());
        } else if (title.equals(pluginTitle)) {
            event.setCancelled(true);
            String worldName = player.getWorld().getName();
            String smpName = extractSMPName(worldName);
            pluginGUI.handleClick(player, event.getRawSlot(), smpName, event.getClick().isRightClick());
        } else if (title.equals(accessTitle)) {
            event.setCancelled(true);
            String worldName = player.getWorld().getName();
            String smpName = extractSMPName(worldName);
            pluginGUI.getAccessGUI().handleClick(player, event.getRawSlot(), smpName);
        }
    }



    @EventHandler(priority = EventPriority.LOWEST)
    public void onCommand(PlayerCommandPreprocessEvent event) {
        Player player = event.getPlayer();
        String worldName = player.getWorld().getName();

        // Check if player is in an SMP world
        if (!isWorldInSMP(worldName) || !database.contains("smps")) {
            return;
        }

        // Extract SMP name from world name
        String smpName = extractSMPName(worldName);

        String command = event.getMessage().substring(1).split(" ")[0].toLowerCase();
        List<String> enabledCommands = config.getStringList("enabled_commands");

        // Allow operators to bypass
        if (player.isOp()) {
            return;
        }

        // Check if command is enabled globally
        if (enabledCommands.contains(command)) {
            return; // Command is allowed
        }

        // Check if command is enabled for any active plugin
        List<String> enabledPlugins = database.getStringList("smps." + smpName + ".enabled_plugins");
        boolean isPluginCommand = false;

        for (String pluginName : enabledPlugins) {
            // Check if player has access to this plugin
            boolean everyone = database.getBoolean("smps." + smpName + ".plugin_settings." + pluginName + ".everyone");
            String ownerUUID = database.getString("smps." + smpName + ".owner");

            // Skip if plugin is owner-only and player is not the owner
            if (!everyone && !player.getUniqueId().toString().equals(ownerUUID)) {
                continue;
            }

            // Check if command is in this plugin's command list
            List<String> pluginCommands = config.getStringList("plugins." + pluginName + ".commands");
            if (pluginCommands.contains(command)) {
                isPluginCommand = true;
                break;
            }
        }

        // Block command if not enabled globally or by any plugin
        if (!isPluginCommand) {
            event.setCancelled(true);
            sendMessage(player, "command_disabled");
        }
    }

    @EventHandler
    public void onPortalTravel(PlayerPortalEvent event) {
        String fromWorld = event.getFrom().getWorld().getName();

        // Check if player is in an SMP world
        if (!isWorldInSMP(fromWorld) || !database.contains("smps")) {
            return;
        }

        // Extract SMP name from world name
        String smpName = extractSMPName(fromWorld);

        if (event.getCause() == PlayerTeleportEvent.TeleportCause.NETHER_PORTAL) {
            String targetWorld = fromWorld.contains("_nether")
                    ? generateWorldName(smpName, "")
                    : generateWorldName(smpName, "_nether");

            Location target = new Location(
                    Bukkit.getWorld(targetWorld),
                    event.getTo().getX(),
                    event.getTo().getY(),
                    event.getTo().getZ()
            );
            event.setTo(target);
        }
        else if (event.getCause() == PlayerTeleportEvent.TeleportCause.END_PORTAL) {
            String targetWorld = fromWorld.contains("_the_end")
                    ? generateWorldName(smpName, "")
                    : generateWorldName(smpName, "_the_end");

            Location target = new Location(
                    Bukkit.getWorld(targetWorld),
                    event.getTo().getX(),
                    event.getTo().getY(),
                    event.getTo().getZ()
            );
            event.setTo(target);
        }
    }



    public String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    private String getPrefix() {
        return colorize(config.getString("messages.prefix", "&8[&bSMP&8] "));
    }

    public void sendMessage(CommandSender sender, String key, String... replacements) {
        String message = config.getString("messages." + key, "Message not found: " + key);
        for (int i = 0; i < replacements.length; i += 2) {
            message = message.replace(replacements[i], replacements[i + 1]);
        }
        sender.sendMessage(getPrefix() + colorize(message));
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Handle console commands
        if (!(sender instanceof Player)) {
            if (args.length >= 4 && args[0].equalsIgnoreCase("create")) {
                // Format: /smp create <player_name> <smp_name> <privacy> <difficulty>
                String playerName = args[1];
                String smpName = args[2];
                String privacy = args[3];
                String difficulty = args.length > 4 ? args[4] : "peaceful";

                // Execute the actual SMP creation
                handleCreateFromConsole(playerName, smpName, privacy, difficulty);
                return true;
            } else if (args.length == 3 && args[0].equalsIgnoreCase("error") && args[2].equalsIgnoreCase("name_already_exists")) {
                // Format: /smp error <player_name> name_already_exists
                String playerName = args[1];
                Player player = Bukkit.getPlayerExact(playerName);

                if (player != null && player.isOnline()) {
                    // Send the name_exists error message to the player
                    sendMessage(player, "name_exists");
                    getLogger().info("Sent 'name_already_exists' error to player: " + playerName);
                } else {
                    getLogger().warning("Cannot send error message to offline player: " + playerName);
                }
                return true;
            }
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 1) {
            return false;
        }

        // Check for command cooldown
        String subCommand = args[0].toLowerCase();
        String fullCommand = "smp " + subCommand;

        // Check if this command has a cooldown
        if (!checkCommandCooldown(player, fullCommand)) {
            return true; // Command is on cooldown
        }

        switch (subCommand) {
            case "agreement":
                if (args.length == 2) {
                    if (args[1].equalsIgnoreCase("accept")) {
                        agreementBookGUI.handleAccept(player);
                        return true;
                    } else if (args[1].equalsIgnoreCase("decline")) {
                        agreementBookGUI.handleDecline(player);
                        return true;
                    }
                }
                return false;

            case "create":
                if (args.length != 1) {
                    return false;
                }
                int playerSmps = 1;
                for (String smpName : database.getConfigurationSection("smps.").getKeys(false)) {
                    if (database.getString("smps." + smpName + ".owner").equals(player.getUniqueId().toString())) {
                        playerSmps++;
                    }
                }
                String autoName = player.getName() + "_" + playerSmps;
                creatorGUI.openGUI(player, autoName);
                break;
            case "join":
                if (args.length == 1) {
                    joinGUI.openGUI(player);
                } else if (args.length == 2) {
                    handleJoin(player, args[1]);
                } else {
                    return false;
                }
                break;

            case "settings":
                String inputName;
                if(args.length == 2) {
                    inputName = args[1];
                } else if(args.length == 1 && isWorldInSMP(player.getWorld().getName())) {
                    inputName = extractSMPName(player.getWorld().getName());
                } else {
                    return false;
                }

                String actualName = null;
                ConfigurationSection smps = database.getConfigurationSection("smps");
                if (smps != null) {
                    for (String name : smps.getKeys(false)) {
                        if (name.equals(inputName) ||
                                smps.getString(name + ".display_name", name).equals(inputName)) {
                            actualName = name;
                            break;
                        }
                    }
                }

                if(actualName == null) {
                    sendMessage(player, "smp_not_found");
                    return true;
                }

                String ownerUUID = database.getString("smps." + actualName + ".owner");
                if(!player.isOp() && !player.getUniqueId().toString().equals(ownerUUID)) {
                    sendMessage(player, "no_settings_permission");
                    return true;
                }

                settingsGUI.openGUI(player, actualName);
                break;

            case "kick":
                if (args.length != 2) {
                    return false;
                }
                handleKick(player, args[1]);
                break;

            case "ban":
                if (args.length != 2) {
                    return false;
                }
                handleBan(player, args[1]);
                break;

            case "unban":
                if (args.length != 2) {
                    return false;
                }
                handleUnban(player, args[1]);
                break;
        }

        // Record command usage for cooldown
        recordCommandUse(player, fullCommand);

        return true;
    }


    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!(sender instanceof Player)) {
            return null;
        }

        if (command.getName().equalsIgnoreCase("smp")) {
            if (args.length == 1) {
                List<String> completions = new ArrayList<>();
                completions.add("create");
                completions.add("join");
                completions.add("settings");
                completions.add("kick");
                completions.add("ban");
                completions.add("unban");
                // Don't add agreement to tab completion as it's meant to be called from the book
                return completions.stream()
                        .filter(s -> s.toLowerCase().startsWith(args[0].toLowerCase()))
                        .collect(Collectors.toList());
            } else if (args.length == 2 && args[0].equalsIgnoreCase("agreement")) {
                List<String> completions = new ArrayList<>();
                completions.add("accept");
                completions.add("decline");
                return completions.stream()
                        .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }

            if (args.length == 2 && (args[0].equalsIgnoreCase("plugins") || args[0].equalsIgnoreCase("settings"))) {
                List<String> completions = new ArrayList<>();
                ConfigurationSection smps = database.getConfigurationSection("smps");
                if (smps != null) {
                    for (String smpName : smps.getKeys(false)) {
                        String displayName = smps.getString(smpName + ".display_name", smpName);
                        completions.add(displayName);
                    }
                }
                return completions.stream()
                        .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }

            if (args.length == 2 && (args[0].equalsIgnoreCase("kick") || args[0].equalsIgnoreCase("ban") || args[0].equalsIgnoreCase("unban"))) {
                List<String> completions = new ArrayList<>();
                // Add online player names for kick and ban
                if (args[0].equalsIgnoreCase("kick") || args[0].equalsIgnoreCase("ban")) {
                    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                        completions.add(onlinePlayer.getName());
                    }
                }
                // For unban, we could add banned players but that would require more complex logic
                // For now, just return online players for all commands
                return completions.stream()
                        .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }
        }
        return null;
    }

    public void handlePrivacyToggle(Player player, String smpName) {
        String currentPrivacy = database.getString("smps." + smpName + ".privacy");
        String newPrivacy = currentPrivacy.equals("public") ? "private" : "public";

        database.set("smps." + smpName + ".privacy", newPrivacy);
        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        sendMessage(player, "smp_privacy_updated", "%privacy%", newPrivacy);
    }

    public void handleDifficultyCycle(Player player, String smpName) {
        String[] difficulties = {"peaceful", "easy", "normal", "hard"};
        String currentDifficulty = database.getString("smps." + smpName + ".difficulty");

        int currentIndex = 0;
        for(int i = 0; i < difficulties.length; i++) {
            if(difficulties[i].equals(currentDifficulty)) {
                currentIndex = i;
                break;
            }
        }

        String newDifficulty = difficulties[(currentIndex + 1) % difficulties.length];
        database.set("smps." + smpName + ".difficulty", newDifficulty);

        // Update world difficulties
        String[] worldTypes = {"", "_nether", "_the_end"};
        for(String type : worldTypes) {
            World world = Bukkit.getWorld(generateWorldName(smpName, type));
            if(world != null) {
                world.setDifficulty(Difficulty.valueOf(newDifficulty.toUpperCase()));
            }
        }

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        sendMessage(player, "smp_difficulty_updated", "%difficulty%", newDifficulty);
    }

    public void handleDelete(Player player, String smpName) {
        // Check creation cooldown
        int maxDelay = Math.max(config.getInt("world_creation.nether_delay"), config.getInt("world_creation.end_delay"));
        long creationTime = database.getLong("smps." + smpName + ".creation_time", 0);
        if (System.currentTimeMillis() - creationTime < maxDelay * 1000L) {
            sendMessage(player, "delete_cooldown");
            return;
        }

        MVWorldManager worldManager = mvCore.getMVWorldManager();
        String[] worldTypes = {"", "_nether", "_the_end"};

        // Teleport all players to main world first
        List<Player> playersToTeleport = new ArrayList<>();

        for (String type : worldTypes) {
            String worldName = generateWorldName(smpName, type);
            World world = Bukkit.getWorld(worldName);
            if (world != null) {
                // Add players from this world dimension to the list
                playersToTeleport.addAll(world.getPlayers());
            }
        }
        // Teleport all collected players
        for (Player p : playersToTeleport) {
            if (p != null && p.isOnline()) { // Double check player state
                p.performCommand("spawn"); // Use EssentialsX spawn command
                sendMessage(p, "smp_world_deleted_teleporting", "%name%", database.getString("smps." + smpName + ".display_name", smpName)); // Inform the player
            }
        }

        // Delete MV inventory group
        new BukkitRunnable() {
            @Override
            public void run() {
                // Delete worlds using MV API
                for (String type : worldTypes) {
                    String worldName = generateWorldName(smpName, type);
                    if (worldManager.isMVWorld(worldName)) {
                        worldManager.deleteWorld(worldName, true, true); // true, true = delete files and remove from config
                    }
                }

                // Manually delete Multiverse-Inventories files
                File mvInvDir = new File(getDataFolder().getParentFile(), "Multiverse-Inventories");

                // Delete group folder
                File groupDir = new File(mvInvDir, "groups/smp_" + smpName);
                if (groupDir.exists()) {
                    deleteDirectory(groupDir);
                }

                // Delete world inventory files
                for (String type : worldTypes) {
                    String worldName = generateWorldName(smpName, type);
                    File worldDir = new File(mvInvDir, "worlds/" + worldName);
                    if (worldDir.exists()) {
                        deleteDirectory(worldDir);
                    }
                }

                ConfigurationSection locationsSection = database.getConfigurationSection("locations");
                if (locationsSection != null) {
                    for (String playerUUID : locationsSection.getKeys(false)) {
                        // Remove this SMP's location data for each player
                        database.set("locations." + playerUUID + "." + smpName, null);

                        // If player has no more locations, remove the player entry
                        if (database.getConfigurationSection("locations." + playerUUID) != null &&
                                database.getConfigurationSection("locations." + playerUUID).getKeys(false).isEmpty()) {
                            database.set("locations." + playerUUID + "." + smpName, null);
                        }
                    }
                }
                // Remove from database
                database.set("smps." + smpName, null);
                try {
                    database.save(databaseFile);
                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }
                pluginManager.cleanupSMP(smpName);

                // Cleanup economy
                multiworldMoneyManager.cleanupSMPEconomy(smpName);

                // Execute the velocity delete command
                executeVelocityDeleteCommand(smpName);

                sendMessage(player, "smp_deleted", "%name%", smpName);
            }
        }.runTaskLater(this, 20L); // 1 second delay to ensure MV inventory deletion completes
    }

    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }


    private void startCreationSafety(Player player) {
        creationKeepInventory.put(player.getUniqueId(), true);

        // Log that safety mode is enabled for this player
        getLogger().info("Creation safety mode enabled for player: " + player.getName());

        // Schedule the removal of safety mode
        Bukkit.getScheduler().runTaskLater(this, () -> {
                creationKeepInventory.remove(player.getUniqueId());
                getLogger().info("Creation safety mode disabled for player: " + player.getName());
            },
            config.getInt("world_creation.keep_inventory_duration") * 20L);
    }

    public void handleCreate(Player player, String name, String privacy, String difficulty) {
        long cooldownSeconds = config.getLong("creation_cooldown", 300);
        if (cooldownSeconds > 0 && !player.hasPermission("smp.cooldown.bypass")) {
            long lastCreationTime = database.getLong("player_data." + player.getUniqueId() + ".last_smp_creation", 0);
            if (lastCreationTime != 0) {
                long cooldownMillis = cooldownSeconds * 1000;
                long currentTime = System.currentTimeMillis();
                long cooldownEndTime = lastCreationTime + cooldownMillis;

                if (currentTime < cooldownEndTime) {
                    long remainingMillis = cooldownEndTime - currentTime;
                    sendMessage(player, "creation_cooldown_active", "%time%", formatTime(remainingMillis));
                    return;
                }
            }
        }
        // Check if SMP exists
        if (database.contains("smps." + name)) {
            sendMessage(player, "smp_exists");
            return;
        }

        // Validate privacy
        if (!privacy.equalsIgnoreCase("private") && !privacy.equalsIgnoreCase("public")) {
            sendMessage(player, "invalid_privacy");
            return;
        }

        // Validate difficulty
        Difficulty diff;
        try {
            diff = Difficulty.valueOf(difficulty.toUpperCase());
        } catch (IllegalArgumentException e) {
            sendMessage(player, "invalid_difficulty");
            return;
        }

        if (difficulty == null) {
            difficulty = "peaceful"; // Default to peaceful if not specified
        }

        // Check permissions
        int maxSmps = getMaxPermissionValue(player, "smp.maxsmp.", 1);
        int currentSmps = 0;
        for (String smpName : database.getConfigurationSection("smps.").getKeys(false)) {
            if (database.getString("smps." + smpName + ".owner").equals(player.getUniqueId().toString())) {
                currentSmps++;
            }
        }

        if (currentSmps >= maxSmps) {
            sendMessage(player, "max_smps_reached");
            return;
        }

        // Show the agreement book to the player
        if (agreementBookGUI != null) {
            agreementBookGUI.openAgreementBook(player, name, privacy, difficulty);
        } else {
            getLogger().warning("Agreement book GUI is null! Falling back to direct creation.");
            executeVelocitySMPCreateCommand(player, name, privacy, difficulty);
            sendMessage(player, "smp_creation_pending", "%name%", name);
        }
    }

    /**
     * Handles SMP creation when initiated from console (after Velocity approval)
     * This method skips the Velocity command execution and proceeds directly with creation
     * @param playerName The name of the player creating the SMP
     * @param smpName The name of the SMP
     * @param privacy The privacy setting (public/private)
     * @param difficulty The difficulty setting
     */
    public void handleCreateFromConsole(String playerName, String smpName, String privacy, String difficulty) {
        // Get the player
        Player player = Bukkit.getPlayerExact(playerName);
        if (player == null) {
            getLogger().warning("Cannot create SMP for offline player: " + playerName);
            return;
        }

        // Validate difficulty
        Difficulty diff;
        try {
            diff = Difficulty.valueOf(difficulty.toUpperCase());
        } catch (IllegalArgumentException e) {
            getLogger().warning("Invalid difficulty for SMP creation: " + difficulty);
            return;
        }

        sendMessage(player, "creating_smp", "%name%", smpName);
        player.sendTitle(
                colorize(config.getString("world_creation.title", "&b&lGenerating your SMP...")),
                colorize(config.getString("world_creation.subtitle", "&7This may take a while")),
                10, 70, 20
        );

        // Create worlds
        startCreationSafety(player);
        MVWorldManager worldManager = mvCore.getMVWorldManager();
        String overworldName = generateWorldName(smpName, "");
        worldManager.addWorld(
                overworldName,
                World.Environment.NORMAL,
                null,
                WorldType.NORMAL,
                true,
                null,
                true
        );

        World overworld = Bukkit.getWorld(overworldName);
        if (overworld != null) {
            overworld.setDifficulty(diff);
            WorldBorder border = overworld.getWorldBorder();
            int borderSize = getMaxPermissionValue(player, "smp.worldsize.", 1000) / 2;
            border.setCenter(0, 0);
            border.setSize(borderSize * 2);
            overworld.setKeepSpawnInMemory(false);
            // Disable achievement announcements in this world
            overworld.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                String netherName = generateWorldName(smpName, "_nether");
                worldManager.addWorld(
                        netherName,
                        World.Environment.NETHER,
                        null,
                        WorldType.NORMAL,
                        true,
                        null,
                        true
                );
                World nether = Bukkit.getWorld(netherName);
                if (nether != null) {
                    nether.setDifficulty(diff);
                    nether.setKeepSpawnInMemory(false);
                    // Disable achievement announcements in this world
                    nether.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mvinv addworld " + netherName + " smp_" + smpName);
                    // Add nether world to economy group
                    multiworldMoneyManager.addWorldToGroup(netherName, smpName);
                }
            }
        }.runTaskLater(this, config.getInt("world_creation.nether_delay") * 20L);

        new BukkitRunnable() {
            @Override
            public void run() {
                String endName = generateWorldName(smpName, "_the_end");
                worldManager.addWorld(
                        endName,
                        World.Environment.THE_END,
                        null,
                        WorldType.NORMAL,
                        true,
                        null,
                        true
                );
                World end = Bukkit.getWorld(endName);
                if (end != null) {
                    end.setDifficulty(diff);
                    end.setKeepSpawnInMemory(false);
                    // Disable achievement announcements in this world
                    end.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mvinv addworld " + endName + " smp_" + smpName);
                    // Add end world to economy group
                    multiworldMoneyManager.addWorldToGroup(endName, smpName);
                }
                long joinDelayTicks = config.getLong("world_creation.join_delay", 5) * 20L;
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        // Mark SMP as no longer loading - creation is complete
                        database.set("smps." + smpName + ".loading", false);
                        try {
                            database.save(databaseFile);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                        // Make sure player is still online before attempting join
                        if (player != null && player.isOnline()) {
                            sendMessage(player, "smp_created", "%name%", smpName);
                            sendMessage(player, "smp_creation_complete_teleporting", "%name%", smpName);
                            handleJoin(player, smpName);
                        }
                    }
                }.runTaskLater(SMP.this, joinDelayTicks);

            }
        }.runTaskLater(this, config.getInt("world_creation.end_delay") * 20L);

        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mvinv creategroup smp_" + smpName);
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mvinv addworld " + overworldName + " smp_" + smpName);
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mvinv addshares all smp_" + smpName);

        // Create economy group and add worlds
        multiworldMoneyManager.createEconomyGroup(smpName);
        multiworldMoneyManager.addWorldToGroup(overworldName, smpName);


        // Save to database
        long currentTime = System.currentTimeMillis();
        database.set("smps." + smpName + ".owner", player.getUniqueId().toString());
        database.set("smps." + smpName + ".privacy", privacy.toLowerCase());
        database.set("smps." + smpName + ".difficulty", difficulty.toLowerCase());
        database.set("smps." + smpName + ".creation_time", currentTime);
        database.set("smps." + smpName + ".last_activity", currentTime);
        database.set("smps." + smpName + ".inactive", false);
        database.set("smps." + smpName + ".display_name", smpName);
        database.set("smps." + smpName + ".loading", true); // Mark as loading during creation
        database.set("smps." + smpName + ".inactivity_notifications", null); // Initialize inactivity notifications section

        // Initialize unique players list with the owner
        List<String> uniquePlayers = new ArrayList<>();
        uniquePlayers.add(player.getUniqueId().toString());
        database.set("smps." + smpName + ".unique_players", uniquePlayers);

        // Initialize average players tracking
        String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        database.set("smps." + smpName + ".average_players." + currentDate + ".total_players_seen", 0);
        database.set("smps." + smpName + ".average_players." + currentDate + ".checks", 0);

        database.set("player_data." + player.getUniqueId() + ".last_smp_creation", currentTime);

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void saveDatabase() {
        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

    }

    public void handleJoin(Player player, String name) {
        debugLog("Player " + player.getName() + " attempting to join SMP: " + name);

        // Check for general SMP join cooldown
        long joinCooldownSeconds = config.getLong("join_cooldown", 5);
        if (joinCooldownSeconds > 0 && !player.hasPermission("smp.cooldown.bypass")) {
            long lastJoinTime = smpJoinCooldownTimestamps.getOrDefault(player.getUniqueId(), 0L);
            if (lastJoinTime != 0) {
                long cooldownMillis = joinCooldownSeconds * 1000;
                long currentTime = System.currentTimeMillis();
                long cooldownEndTime = lastJoinTime + cooldownMillis;

                if (currentTime < cooldownEndTime) {
                    long remainingMillis = cooldownEndTime - currentTime;
                    debugLog("Player " + player.getName() + " has " + formatTime(remainingMillis) +
                            " remaining on join cooldown");
                    sendMessage(player, "join_cooldown_active", "%time%", formatTime(remainingMillis));
                    return;
                } else {
                    debugLog("Player " + player.getName() + " join cooldown has expired");
                }
            } else {
                debugLog("Player " + player.getName() + " has no previous join timestamp");
            }
        } else {
            debugLog("Join cooldown check skipped: cooldown=" + joinCooldownSeconds +
                    ", bypass=" + player.hasPermission("smp.cooldown.bypass"));
        }

        // Check for combat-based SMP join cooldown
        long combatJoinCooldownSeconds = config.getLong("combat_join_cooldown", 10);
        if (combatJoinCooldownSeconds > 0 && !player.hasPermission("smp.cooldown.bypass")) {
            long lastCombatTime = combatCooldownTimestamps.getOrDefault(player.getUniqueId(), 0L);
            if (lastCombatTime != 0) {
                long cooldownMillis = combatJoinCooldownSeconds * 1000;
                long currentTime = System.currentTimeMillis();
                long cooldownEndTime = lastCombatTime + cooldownMillis;

                if (currentTime < cooldownEndTime) {
                    long remainingMillis = cooldownEndTime - currentTime;
                    debugLog("Player " + player.getName() + " has " + formatTime(remainingMillis) +
                            " remaining on combat join cooldown");
                    sendMessage(player, "combat_join_cooldown_active", "%time%", formatTime(remainingMillis));
                    return;
                } else {
                    // Cooldown expired, remove timestamp
                    debugLog("Player " + player.getName() + " combat cooldown has expired");
                    combatCooldownTimestamps.remove(player.getUniqueId());
                }
            } else {
                debugLog("Player " + player.getName() + " has no combat timestamp");
            }
        } else {
            debugLog("Combat join cooldown check skipped: cooldown=" + combatJoinCooldownSeconds +
                    ", bypass=" + player.hasPermission("smp.cooldown.bypass"));
        }

        // Check if player is already in an SMP world
        String currentWorld = player.getWorld().getName();
        if (isWorldInSMP(currentWorld)) {
            String currentSMP = extractSMPName(currentWorld);

            // Check if the SMP they're trying to join is the one they're already in
            if (currentSMP != null && (currentSMP.equals(name) ||
                    database.getString("smps." + currentSMP + ".display_name", currentSMP).equals(name))) {
                sendMessage(player, "already_in_smp", "%name%", name);
                return;
            }
        }

        ConfigurationSection smps = database.getConfigurationSection("smps");
        String actualName = null;

        if (smps != null) {
            for (String smpName : smps.getKeys(false)) {
                if (smpName.equals(name) ||
                        smps.getString(smpName + ".display_name", smpName).equals(name)) {
                    actualName = smpName;
                    break;
                }
            }
        }

        if (actualName == null) {
            sendMessage(player, "smp_not_found");
            return;
        }

        // Check if SMP is still loading
        boolean isLoading = database.getBoolean("smps." + actualName + ".loading", false);
        if (isLoading) {
            sendMessage(player, "smp_still_loading", "%name%", database.getString("smps." + actualName + ".display_name", actualName));
            return;
        }

        String ownerUUID = database.getString("smps." + actualName + ".owner");
        String privacy = database.getString("smps." + actualName + ".privacy");

        // Check clan access for private SMPs
        if (privacy.equals("private") && clansAPI != null) {
            // Get the offline player object first
            OfflinePlayer ownerOffline = Bukkit.getOfflinePlayer(UUID.fromString(ownerUUID));

            // Check if the owner is online
            Player ownerPlayer = ownerOffline.isOnline() ? ownerOffline.getPlayer() : null;

            // Get clans safely
            Clan ownerClan = null;
            if (ownerPlayer != null) {
                // If owner is online, use getClanByBukkitPlayer
                ownerClan = clansAPI.getClanByBukkitPlayer(ownerPlayer);
            } else {
                // If owner is offline, use getClanByBukkitOfflinePlayerOwner
                // This is the best we can do for offline players
                ownerClan = clansAPI.getClanByBukkitOfflinePlayerOwner(ownerOffline);
            }
            Clan playerClan = clansAPI.getClanByBukkitPlayer(player);

            // Check if both players are in the same clan
            if (ownerClan == null || playerClan == null || !ownerClan.equals(playerClan)) {
                sendMessage(player, "not_in_clan");
                return;
            }
        }

        // Check if player is banned from this SMP
        List<String> bannedPlayers = database.getStringList("smps." + actualName + ".banned_players");
        if (bannedPlayers.contains(player.getUniqueId().toString())) {
            sendMessage(player, "banned_from_smp_join", "%smp%", database.getString("smps." + actualName + ".display_name", actualName));
            return;
        }

        int maxPlayers = getMaxPermissionValue(Bukkit.getOfflinePlayer(UUID.fromString(ownerUUID)),
                "smp.maxplayers.", 10);

        int currentPlayers = countSMPPlayers(actualName);

        if (currentPlayers >= maxPlayers) {
            sendMessage(player, "max_players_reached");
            return;
        }
        Location targetLoc = getLastLocation(player, actualName);
        World world = targetLoc.getWorld();

        String finalActualName = actualName;
        new BukkitRunnable() {
            @Override
            public void run() {
                // Load chunk and teleport on main thread
                world.loadChunk(targetLoc.getChunk().getX(), targetLoc.getChunk().getZ());
                player.teleport(targetLoc);
                pluginManager.handlePlayerJoin(player, finalActualName);
                sendMessage(player, "smp_joined", "%name%", database.getString("smps." + finalActualName + ".display_name", finalActualName));

                // Update tab visibility for all players
                for (Player p : Bukkit.getOnlinePlayers()) {
                    updateTabVisibility(p);
                }

                // Update SMP activity when player joins
                updateSMPActivity(finalActualName);

                // Track unique player join
                trackUniquePlayerJoin(finalActualName, player.getUniqueId());

                // Handle first-time join economy reset
                multiworldMoneyManager.handlePlayerFirstJoin(player, finalActualName);

                // Record the join timestamp for cooldown
                smpJoinCooldownTimestamps.put(player.getUniqueId(), System.currentTimeMillis());
                debugLog("Recorded SMP join timestamp for player " + player.getName() +
                        " with cooldown of " + config.getLong("join_cooldown", 5) + " seconds");

                // Handle automatic vanish for OP players or players with vanish permission
                handleAutoVanish(player);
            }
        }.runTask(this);
    }

    /**
     * Handles automatic vanish mode for OP players or players with vanish permission
     * @param player The player to potentially put in vanish mode
     */
    private void handleAutoVanish(Player player) {
        // Check if player has OP or the invisible permission
        String invisiblePermission = config.getString("invisible_permission", "smp.invisible");
        if (player.isOp() || player.hasPermission(invisiblePermission)) {
            String vanishCommand = config.getString("vanish_command", "vanish");

            // Execute the vanish command for the player
            // Use a slight delay to ensure the player is fully loaded into the SMP
            new BukkitRunnable() {
                @Override
                public void run() {
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), vanishCommand + " " + player.getName());
                    debugLog("Auto-vanished player " + player.getName() + " upon SMP join");
                }
            }.runTaskLater(this, 5L); // 5 tick delay (0.25 seconds)
        }
    }

    public void handleDisplayNameChange(Player player, String smpName, String newDisplayName) {
        database.set("smps." + smpName + ".display_name", newDisplayName);
        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        sendMessage(player, "display_name_updated", "%name%", newDisplayName);
    }

    /**
     * Handles actual SMP renaming (changes the internal name, not just display name)
     * This is needed for proper economy group management
     * @param player The player requesting the rename
     * @param oldSmpName The current SMP name
     * @param newSmpName The new SMP name
     */
    public void handleSMPRename(Player player, String oldSmpName, String newSmpName) {
        // Check if new name already exists
        if (database.contains("smps." + newSmpName)) {
            sendMessage(player, "smp_exists");
            return;
        }

        // Validate new name
        if (!newSmpName.matches("^[a-zA-Z0-9_]+$") || newSmpName.length() > 16) {
            sendMessage(player, "invalid_smp_name");
            return;
        }

        // Check if player owns the SMP
        String ownerUUID = database.getString("smps." + oldSmpName + ".owner");
        if (!ownerUUID.equals(player.getUniqueId().toString())) {
            sendMessage(player, "not_smp_owner");
            return;
        }

        sendMessage(player, "smp_renaming", "%old%", oldSmpName, "%new%", newSmpName);

        // Handle economy group renaming
        multiworldMoneyManager.handleSMPRename(oldSmpName, newSmpName);

        // Copy all SMP data to new name
        ConfigurationSection oldSmpData = database.getConfigurationSection("smps." + oldSmpName);
        if (oldSmpData != null) {
            database.set("smps." + newSmpName, oldSmpData);
            database.set("smps." + newSmpName + ".display_name", newSmpName); // Update display name too
        }

        // Update player location data
        ConfigurationSection locationsSection = database.getConfigurationSection("locations");
        if (locationsSection != null) {
            for (String playerUUID : locationsSection.getKeys(false)) {
                ConfigurationSection playerLocations = database.getConfigurationSection("locations." + playerUUID);
                if (playerLocations != null && playerLocations.contains(oldSmpName)) {
                    ConfigurationSection oldLocationData = playerLocations.getConfigurationSection(oldSmpName);
                    database.set("locations." + playerUUID + "." + newSmpName, oldLocationData);
                    database.set("locations." + playerUUID + "." + oldSmpName, null);
                }
            }
        }

        // Remove old SMP data
        database.set("smps." + oldSmpName, null);

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            sendMessage(player, "smp_rename_error");
            return;
        }

        sendMessage(player, "smp_renamed", "%old%", oldSmpName, "%new%", newSmpName);
    }

    public int countSMPPlayers(String name) {
        int players = 0;
        String[] worldTypes = {"", "_nether", "_the_end"};

        for (String type : worldTypes) {
            World world = Bukkit.getWorld(generateWorldName(name, type));
            if(world != null) {
                players += world.getPlayers().size();
            }
        }

        return players;
    }

    /**
     * Counts the number of unique players who have ever joined an SMP
     * @param smpName The name of the SMP to check
     * @return The number of unique players who have joined the SMP
     */
    public int countUniquePlayers(String smpName) {
        int uniquePlayers = 0;

        // Check if the SMP has a unique_players section
        if (database.contains("smps." + smpName + ".unique_players")) {
            // Get the list of UUIDs
            List<String> playerUUIDs = database.getStringList("smps." + smpName + ".unique_players");
            uniquePlayers = playerUUIDs.size();
        }

        return uniquePlayers;
    }

    private void savePlayerLocation(Player player, Location loc) {
        String worldName = loc.getWorld().getName();
        if (!isWorldInSMP(worldName)) return;

        String smpName = extractSMPName(worldName);

        database.set("locations." + player.getUniqueId() + "." + smpName + ".world", worldName);
        database.set("locations." + player.getUniqueId() + "." + smpName + ".x", loc.getX());
        database.set("locations." + player.getUniqueId() + "." + smpName + ".y", loc.getY());
        database.set("locations." + player.getUniqueId() + "." + smpName + ".z", loc.getZ());
        database.set("locations." + player.getUniqueId() + "." + smpName + ".yaw", loc.getYaw());
        database.set("locations." + player.getUniqueId() + "." + smpName + ".pitch", loc.getPitch());

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private Location getLastLocation(Player player, String smpName) {
        String path = "locations." + player.getUniqueId() + "." + smpName;
        if (!database.contains(path)) {
            // --- Start of changed block ---
            World targetWorld = Bukkit.getWorld(generateWorldName(smpName, ""));
            if (targetWorld == null) {
                // Should not happen if creation succeeded, but handle gracefully
                getLogger().warning("Target world " + generateWorldName(smpName, "") + " is null during initial spawn calculation!");
                return Bukkit.getWorlds().get(0).getSpawnLocation(); // Absolute fallback
            }

            // Prioritize world spawn if it's already set and seems valid
            Location worldSpawn = targetWorld.getSpawnLocation();
            if (worldSpawn != null && isLocationSafe(worldSpawn)) {
                // Ensure it's within the border (Multiverse usually sets spawn to 0,0 anyway)
                WorldBorder border = targetWorld.getWorldBorder();
                if (border.isInside(worldSpawn)) {
                    return worldSpawn.clone().add(0.5, 0, 0.5); // Center on block
                }
            }

            // Find a safe spot near the center (0,0) if world spawn isn't good
            Location safeSpawn = findSafeSpawnNearCenter(targetWorld);
            if (safeSpawn == null) {
                getLogger().severe("Could not find ANY safe spawn location near center for " +
                                 targetWorld.getName() + ". Defaulting to absolute fallback.");
                safeSpawn = Bukkit.getWorlds().get(0).getSpawnLocation(); // Absolute fallback
            }

            // Set the world spawn explicitly for future reference
            targetWorld.setSpawnLocation(safeSpawn);
            getLogger().info("Set initial spawn for " + targetWorld.getName() + " to: " + safeSpawn.toString());

            return safeSpawn;
        }

        String world = database.getString(path + ".world");
        double x = database.getDouble(path + ".x");
        double y = database.getDouble(path + ".y");
        double z = database.getDouble(path + ".z");
        float yaw = (float) database.getDouble(path + ".yaw");
        float pitch = (float) database.getDouble(path + ".pitch");

        World targetWorld = Bukkit.getWorld(world);
        if (targetWorld == null) {
            targetWorld = Bukkit.getWorld(generateWorldName(smpName, ""));
            Location spawnLoc = targetWorld.getSpawnLocation();

            if (spawnLoc == null) {
                int spawnY = targetWorld.getHighestBlockYAt(0, 0) + 2;
                return new Location(targetWorld, 0, spawnY, 0);
            }
            return spawnLoc;
        }

        return new Location(targetWorld, x, y, z, yaw, pitch);
    }

    /**
     * Checks if a location is safe to spawn at (solid block below, two air blocks above).
     */
    private boolean isLocationSafe(Location loc) {
        if (loc == null || loc.getWorld() == null) return false;
        World world = loc.getWorld();
        int x = loc.getBlockX();
        int y = loc.getBlockY();
        int z = loc.getBlockZ();

        // Check block below feet (needs to be solid)
        Material below = world.getBlockAt(x, y - 1, z).getType();
        // Check feet level and head level (needs to be non-solid, preferably air)
        Material feet = world.getBlockAt(x, y, z).getType();
        Material head = world.getBlockAt(x, y + 1, z).getType();

        // Allow common non-solid blocks like grass, flowers at feet level
        boolean solidBelow = below.isSolid() && below != Material.BARRIER; // Ensure solid ground
        boolean safeFeet = !feet.isSolid() || feet == Material.WATER; // Allow water, air, grass, etc.
        boolean safeHead = !head.isSolid() || head == Material.WATER; // Allow water, air, etc.

        return solidBelow && safeFeet && safeHead;
    }


    /**
     * Searches for a safe spawn location near the world center (0,0).
     */
    private Location findSafeSpawnNearCenter(World world) {
        if (world == null) return null;

        WorldBorder border = world.getWorldBorder();
        int centerX = (int) border.getCenter().getX();
        int centerZ = (int) border.getCenter().getZ();
        int searchRadius = 10; // Search up to 10 blocks away from center

        for (int r = 0; r <= searchRadius; r++) {
            for (int dx = -r; dx <= r; dx++) {
                for (int dz = -r; dz <= r; dz++) {
                    // Only check the perimeter of the current radius square to expand outwards
                    if (Math.abs(dx) != r && Math.abs(dz) != r) {
                        continue;
                    }

                    int x = centerX + dx;
                    int z = centerZ + dz;

                    // Ensure the check location is within the world border
                    Location checkLocXZ = new Location(world, x, 0, z); // Y doesn't matter for isInside
                    if (!border.isInside(checkLocXZ)) {
                        continue;
                    }

                    // Get the highest solid block's Y coordinate at this X, Z
                    // Note: getHighestBlockYAt returns the first *air* block, so subtract 1 for the ground
                    int y = world.getHighestBlockYAt(x, z);

                    Location potentialSpawn = new Location(world, x + 0.5, y, z + 0.5); // Feet position

                    if (isLocationSafe(potentialSpawn)) {
                        return potentialSpawn; // Found a safe spot
                    }
                }
            }
        }

        // Fallback if no safe spot found within radius (very unlikely)
        // Try center directly, even if isLocationSafe failed initially, maybe highest block logic works better now
        int centerY = world.getHighestBlockYAt(centerX, centerZ);
        Location centerLoc = new Location(world, centerX + 0.5, centerY, centerZ + 0.5);
        if (isLocationSafe(centerLoc)) return centerLoc; // One last try at the exact center

        getLogger().warning("Could not find safe spawn within " + searchRadius + " blocks of center for " + world.getName());
        // Return a location at 0, highestY, 0 as an absolute last resort before null
        return new Location(world, centerX + 0.5, world.getHighestBlockYAt(centerX, centerZ), centerZ + 0.5);
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        String worldName = player.getWorld().getName();

        // Check if player is in an SMP world
        if(isWorldInSMP(worldName)) {
            String smpName = extractSMPName(worldName);
            boolean isInCreationSafety = creationKeepInventory.containsKey(player.getUniqueId());

            // Store the original death message before potentially modifying it
            String originalDeathMessage = event.getDeathMessage();

            // Always set death message to null to prevent global broadcast
            event.setDeathMessage(null);

            // Send death message only to players in the same SMP (if not in creation safety)
            if (!isInCreationSafety && originalDeathMessage != null && !originalDeathMessage.isEmpty()) {
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    String playerWorld = onlinePlayer.getWorld().getName();
                    if (isWorldInSMP(playerWorld)) {
                        String playerSMP = extractSMPName(playerWorld);
                        if (playerSMP.equals(smpName)) {
                            onlinePlayer.sendMessage(originalDeathMessage);
                        }
                    }
                }
            }

            if(isInCreationSafety) {
                // CASE 1: Player is in creation safety mode
                // Keep inventory and give immunity
                event.setKeepInventory(true);
                event.setKeepLevel(true);
                event.setDroppedExp(0);
                event.getDrops().clear();
                // Death message already set to null above

                // Create book
                BookMeta meta = (BookMeta) Bukkit.getItemFactory().getItemMeta(Material.WRITTEN_BOOK);
                meta.setTitle(colorize(config.getString("world_creation.death_book.title")));
                meta.setAuthor(config.getString("world_creation.death_book.author"));
                meta.setPages(config.getStringList("world_creation.death_book.pages").stream()
                        .map(this::colorize)
                        .collect(Collectors.toList()));

                ItemStack book = new ItemStack(Material.WRITTEN_BOOK);
                book.setItemMeta(meta);

                // Get a safe location near the death point
                Location deathLoc = player.getLocation();
                Location safe = findSafeLocationNearDeath(deathLoc);

                // Log the teleportation for debugging
                getLogger().info("Teleporting player " + player.getName() + " to safe location: " +
                        safe.getWorld().getName() + " at " + safe.getX() + ", " + safe.getY() + ", " + safe.getZ());

                // Bypass death screen by teleporting immediately and restoring health
                Bukkit.getScheduler().runTaskLater(this, () -> {
                    // Restore player health and teleport
                    player.setHealth(player.getMaxHealth());
                    player.teleport(safe);

                    // Open the book immediately after teleport
                    Bukkit.getScheduler().runTaskLater(this, () -> {
                        player.openBook(book);
                    }, 5L);

                    // Start immunity period
                    startImmunity(player);
                    player.sendMessage(colorize("&aYou died during world creation and have been teleported to a safe location."));
                    player.sendMessage(colorize("&aYou have 30 seconds of immunity."));
                }, 1L); // Run on the next tick
            } else if(config.getBoolean("safe_respawn.bypass_death_screen", true)) {
                // CASE 2: Regular player in SMP world with bypass_death_screen enabled
                // We don't need to do anything special here - the player will respawn at their bed
                // or the SMP spawn point automatically through the normal respawn process

                // Just log that a player died in an SMP world
                getLogger().info("Player " + player.getName() + " died in SMP world. Normal respawn will occur.");
            }
            // For all other cases, let the normal death process happen
        }
    }

    private Location getSafeLocation(Location death) {
        // Always teleport to the main world spawn when a player dies during world creation
        // This ensures they don't get stuck in the SMP world that's being created
        World mainWorld = Bukkit.getWorlds().get(0); // Get the main world (usually 'world')
        Location spawnLoc = mainWorld.getSpawnLocation();

        // Ensure the spawn location is safe
        if (spawnLoc != null && isLocationSafe(spawnLoc)) {
            return spawnLoc.clone().add(0.5, 0, 0.5); // Center on block
        }

        // If the spawn location isn't safe, find a safe location near it
        int x = spawnLoc.getBlockX();
        int y = spawnLoc.getBlockY();
        int z = spawnLoc.getBlockZ();

        // Try to find a safe spot near the spawn
        for(int dy = 0; dy <= 5; dy++) {
            for(int dx = -5; dx <= 5; dx++) {
                for(int dz = -5; dz <= 5; dz++) {
                    Location loc = new Location(mainWorld, x + dx, y + dy, z + dz);
                    if(loc.getBlock().getType().isSolid() &&
                            loc.clone().add(0, 1, 0).getBlock().getType().isAir() &&
                            loc.clone().add(0, 2, 0).getBlock().getType().isAir()) {
                        return new Location(mainWorld, x + dx + 0.5, y + dy + 1, z + dz + 0.5);
                    }
                }
            }
        }

        // Last resort: get the highest block at spawn coordinates and ensure it's safe
        int highestY = mainWorld.getHighestBlockYAt(x, z);
        return new Location(mainWorld, x + 0.5, highestY + 1, z + 0.5);
    }

    /**
     * Finds a safe location near the player's death point in the same world.
     * @param deathLoc The location where the player died
     * @return A safe location near the death point, or null if none found
     */
    private Location findSafeLocationNearDeath(Location deathLoc) {
        if (deathLoc == null) return null;

        World world = deathLoc.getWorld();
        int x = deathLoc.getBlockX();
        int y = deathLoc.getBlockY();
        int z = deathLoc.getBlockZ();

        // First, check if the death location itself is safe
        Location exactLoc = new Location(world, x + 0.5, y, z + 0.5);
        if (isLocationSafe(exactLoc)) {
            return exactLoc;
        }

        // Search in a spiral pattern around the death location
        int searchRadius = config.getInt("safe_respawn.search_radius", 20); // Get search radius from config

        // Try to find a safe spot near the death location
        for (int radius = 1; radius <= searchRadius; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    // Only check the perimeter of the current radius
                    if (Math.abs(dx) != radius && Math.abs(dz) != radius) continue;

                    // Get the highest solid block at this X,Z coordinate
                    int highestY = world.getHighestBlockYAt(x + dx, z + dz);
                    Location potentialLoc = new Location(world, x + dx + 0.5, highestY + 1, z + dz + 0.5);

                    if (isLocationSafe(potentialLoc)) {
                        return potentialLoc;
                    }

                    // Also check a few blocks up and down from the highest point
                    for (int dy = -3; dy <= 3; dy++) {
                        Location checkLoc = new Location(world, x + dx + 0.5, highestY + dy, z + dz + 0.5);
                        if (isLocationSafe(checkLoc)) {
                            return checkLoc;
                        }
                    }
                }
            }
        }

        // If no safe location found, return the world spawn as a fallback
        return world.getSpawnLocation();
    }

    private void startImmunity(Player player) {
        BossBar bar = Bukkit.createBossBar(
                colorize(config.getString("world_creation.immunity.bossbar.title").replace("%seconds%", "30")),
                BarColor.valueOf(config.getString("world_creation.immunity.bossbar.color")),
                BarStyle.valueOf(config.getString("world_creation.immunity.bossbar.style"))
        );

        bar.addPlayer(player);
        immunityBars.put(player.getUniqueId(), bar);

        new BukkitRunnable() {
            int seconds = 30;

            @Override
            public void run() {
                if(seconds <= 0) {
                    bar.removeAll();
                    immunityBars.remove(player.getUniqueId());
                    cancel();
                    return;
                }

                bar.setTitle(colorize(config.getString("world_creation.immunity.bossbar.title")
                        .replace("%seconds%", String.valueOf(seconds))));
                bar.setProgress(seconds / 30.0);
                seconds--;
            }
        }.runTaskTimer(this, 0L, 20L);
    }
    @EventHandler
    public void onEntityDamage(EntityDamageEvent event) {
        if(event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            if(immunityBars.containsKey(player.getUniqueId())) {
                event.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onPlayerAdvancementDone(PlayerAdvancementDoneEvent event) {
        // Achievement messages are disabled via gamerule announceAdvancements = false
        // This gamerule is set when SMP worlds are created
        // No additional processing needed here as the gamerule handles suppression
    }

    @EventHandler
    public void onPlayerLeave(PlayerQuitEvent event) {
        handlePlayerExit(event.getPlayer(), event.getPlayer().getWorld(), event.getPlayer().getLocation());
    }


    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        World fromWorld = event.getFrom().getWorld();

        if (event.getFrom().getWorld() != event.getTo().getWorld()) {
            if(isWorldInSMP(fromWorld.getName())) {
                handlePlayerExit(event.getPlayer(), fromWorld, event.getFrom());
            }
            new BukkitRunnable() {
                @Override
                public void run() {
                    updateTabVisibility(event.getPlayer());
                }
            }.runTask(this);
        }
    }

    private void handlePlayerExit(Player player, World playerWorld, Location location) {
        String worldName = playerWorld.getName();
        if(!isWorldInSMP(worldName)) return;

        savePlayerLocation(player, location);
        String smpName = extractSMPName(worldName);
        String[] worldTypes = {"", "_nether", "_the_end"};

        // Check if any players remain in any of the SMP worlds
        boolean hasPlayers = false;

        for(String type : worldTypes) {
            World world = Bukkit.getWorld(generateWorldName(smpName, type));
            if(world != null && !world.getPlayers().isEmpty()) {
                hasPlayers = true;
                break;
            }
        }

        // If no players remain, unload all chunks in all SMP worlds
        if(!hasPlayers) {
            for(String type : worldTypes) {
                World world = Bukkit.getWorld(generateWorldName(smpName, type));
                if(world != null) {
                    for(Chunk chunk : world.getLoadedChunks()) {
                        chunk.unload(true);
                    }
                }
            }
        }
    }


    public int getMaxPermissionValue(OfflinePlayer player, String permPrefix, int defaultValue) {
        if (!player.isOnline()) {
            return defaultValue;
        }

        String permissionString = "";
        for (String perm : player.getPlayer().getEffectivePermissions().stream()
                .map(permissionAttachmentInfo -> permissionAttachmentInfo.getPermission())
                .filter(permission -> permission.startsWith(permPrefix))
                .collect(java.util.stream.Collectors.toList())) {
            String numberPart = perm.substring(permPrefix.length());
            if (numberPart.matches("\\d+")) {
                int value = Integer.parseInt(numberPart);
                if (value > Integer.parseInt(permissionString.isEmpty() ? "0" : permissionString)) {
                    permissionString = numberPart;
                }
            }
        }

        return permissionString.isEmpty() ? defaultValue : Integer.parseInt(permissionString);
    }

    public ClansLiteAPI getClansAPI() {
        return clansAPI;
    }

    public FileConfiguration getDatabase() {
        return database;
    }

    /**
     * Handles kicking a player from the SMP
     * @param sender The player executing the command (must be SMP owner)
     * @param targetPlayerName The name of the player to kick
     */
    public void handleKick(Player sender, String targetPlayerName) {
        // Check if sender is in an SMP world
        String senderWorld = sender.getWorld().getName();
        if (!isWorldInSMP(senderWorld)) {
            sendMessage(sender, "not_in_smp_world");
            return;
        }

        String smpName = extractSMPName(senderWorld);

        // Check if sender is the SMP owner
        String ownerUUID = database.getString("smps." + smpName + ".owner");
        if (!sender.isOp() && !sender.getUniqueId().toString().equals(ownerUUID)) {
            sendMessage(sender, "not_smp_owner");
            return;
        }

        // Find the target player
        Player targetPlayer = Bukkit.getPlayerExact(targetPlayerName);
        if (targetPlayer == null) {
            sendMessage(sender, "player_not_found");
            return;
        }

        // Check if target player is in the same SMP
        String targetWorld = targetPlayer.getWorld().getName();
        if (!isWorldInSMP(targetWorld) || !extractSMPName(targetWorld).equals(smpName)) {
            sendMessage(sender, "player_not_in_smp");
            return;
        }

        // Don't allow kicking the owner
        if (targetPlayer.getUniqueId().toString().equals(ownerUUID)) {
            sendMessage(sender, "cannot_kick_owner");
            return;
        }

        // Teleport player to spawn
        targetPlayer.performCommand("spawn");

        // Send messages
        String displayName = database.getString("smps." + smpName + ".display_name", smpName);
        sendMessage(targetPlayer, "kicked_from_smp", "%smp%", displayName, "%owner%", sender.getName());
        sendMessage(sender, "player_kicked", "%player%", targetPlayer.getName(), "%smp%", displayName);

        getLogger().info("Player " + targetPlayer.getName() + " was kicked from SMP " + smpName + " by " + sender.getName());
    }

    /**
     * Handles banning a player from the SMP
     * @param sender The player executing the command (must be SMP owner)
     * @param targetPlayerName The name of the player to ban
     */
    public void handleBan(Player sender, String targetPlayerName) {
        // Check if sender is in an SMP world
        String senderWorld = sender.getWorld().getName();
        if (!isWorldInSMP(senderWorld)) {
            sendMessage(sender, "not_in_smp_world");
            return;
        }

        String smpName = extractSMPName(senderWorld);

        // Check if sender is the SMP owner
        String ownerUUID = database.getString("smps." + smpName + ".owner");
        if (!sender.isOp() && !sender.getUniqueId().toString().equals(ownerUUID)) {
            sendMessage(sender, "not_smp_owner");
            return;
        }

        // Find the target player (can be offline)
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(targetPlayerName);
        if (targetPlayer == null) {
            // Try to find online player
            Player onlineTarget = Bukkit.getPlayerExact(targetPlayerName);
            if (onlineTarget != null) {
                targetPlayer = onlineTarget;
            } else {
                sendMessage(sender, "player_not_found");
                return;
            }
        }

        // Don't allow banning the owner
        if (targetPlayer.getUniqueId().toString().equals(ownerUUID)) {
            sendMessage(sender, "cannot_ban_owner");
            return;
        }

        // Get or create banned players list
        List<String> bannedPlayers = database.getStringList("smps." + smpName + ".banned_players");

        // Check if player is already banned
        if (bannedPlayers.contains(targetPlayer.getUniqueId().toString())) {
            sendMessage(sender, "player_already_banned");
            return;
        }

        // Add player to banned list
        bannedPlayers.add(targetPlayer.getUniqueId().toString());
        database.set("smps." + smpName + ".banned_players", bannedPlayers);

        // If player is online and in the SMP, kick them
        if (targetPlayer.isOnline()) {
            Player onlineTarget = targetPlayer.getPlayer();
            String targetWorld = onlineTarget.getWorld().getName();
            if (isWorldInSMP(targetWorld) && extractSMPName(targetWorld).equals(smpName)) {
                onlineTarget.performCommand("spawn");
                String displayName = database.getString("smps." + smpName + ".display_name", smpName);
                sendMessage(onlineTarget, "banned_from_smp", "%smp%", displayName, "%owner%", sender.getName());
            }
        }

        // Save database
        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            sendMessage(sender, "ban_save_error");
            return;
        }

        // Send confirmation message
        String displayName = database.getString("smps." + smpName + ".display_name", smpName);
        sendMessage(sender, "player_banned", "%player%", targetPlayerName, "%smp%", displayName);

        getLogger().info("Player " + targetPlayerName + " was banned from SMP " + smpName + " by " + sender.getName());
    }

    /**
     * Handles unbanning a player from the SMP
     * @param sender The player executing the command (must be SMP owner)
     * @param targetPlayerName The name of the player to unban
     */
    public void handleUnban(Player sender, String targetPlayerName) {
        // Check if sender is in an SMP world
        String senderWorld = sender.getWorld().getName();
        if (!isWorldInSMP(senderWorld)) {
            sendMessage(sender, "not_in_smp_world");
            return;
        }

        String smpName = extractSMPName(senderWorld);

        // Check if sender is the SMP owner
        String ownerUUID = database.getString("smps." + smpName + ".owner");
        if (!sender.isOp() && !sender.getUniqueId().toString().equals(ownerUUID)) {
            sendMessage(sender, "not_smp_owner");
            return;
        }

        // Find the target player (can be offline)
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(targetPlayerName);
        if (targetPlayer == null) {
            // Try to find online player
            Player onlineTarget = Bukkit.getPlayerExact(targetPlayerName);
            if (onlineTarget != null) {
                targetPlayer = onlineTarget;
            } else {
                sendMessage(sender, "player_not_found");
                return;
            }
        }

        // Get banned players list
        List<String> bannedPlayers = database.getStringList("smps." + smpName + ".banned_players");

        // Check if player is actually banned
        if (!bannedPlayers.contains(targetPlayer.getUniqueId().toString())) {
            sendMessage(sender, "player_not_banned");
            return;
        }

        // Remove player from banned list
        bannedPlayers.remove(targetPlayer.getUniqueId().toString());
        database.set("smps." + smpName + ".banned_players", bannedPlayers);

        // Save database
        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
            sendMessage(sender, "unban_save_error");
            return;
        }

        // Send confirmation message
        String displayName = database.getString("smps." + smpName + ".display_name", smpName);
        sendMessage(sender, "player_unbanned", "%player%", targetPlayerName, "%smp%", displayName);

        getLogger().info("Player " + targetPlayerName + " was unbanned from SMP " + smpName + " by " + sender.getName());
    }

    /**
     * Starts the inactivity checker task that runs periodically
     */
    public void startInactivityChecker() {
        int checkInterval = config.getInt("inactivity.check_interval", 3600); // Default: hourly (3600 seconds)
        new BukkitRunnable() {
            @Override
            public void run() {
                checkInactiveSMPs();
            }
        }.runTaskTimer(this, 20L, checkInterval * 20L); // Convert seconds to ticks
        getLogger().info("Started SMP inactivity checker (interval: " + checkInterval + " seconds)");
    }

    /**
     * Checks all SMPs for inactivity and marks them as inactive or deletes them if needed
     * Also checks for specific inactivity thresholds and sends notifications
     */
    public void checkInactiveSMPs() {
        ConfigurationSection smps = database.getConfigurationSection("smps");
        if (smps == null) return;

        long currentTime = System.currentTimeMillis();
        int inactivityDuration = config.getInt("inactivity.duration", 15) * 60 * 1000; // Convert minutes to milliseconds
        int autoDeletionDays = config.getInt("inactivity.auto_delete", 30);
        long autoDeletionMillis = autoDeletionDays * 24 * 60 * 60 * 1000L; // Convert days to milliseconds

        // Get inactivity thresholds from config
        List<Integer> inactivityThresholds = config.getIntegerList("inactivity.thresholds");
        if (inactivityThresholds.isEmpty()) {
            // Default thresholds if not specified in config
            inactivityThresholds = Arrays.asList(4, 8, 11, 14, 17, 20, 23, 26, 28);
        }

        for (String smpName : smps.getKeys(false)) {
            // Get the last activity time
            long lastActivity = database.getLong("smps." + smpName + ".last_activity", 0);
            if (lastActivity == 0) {
                // If no activity recorded yet, use creation time
                lastActivity = database.getLong("smps." + smpName + ".creation_time", currentTime);
                database.set("smps." + smpName + ".last_activity", lastActivity);
            }

            // Check if SMP is inactive
            boolean wasInactive = database.getBoolean("smps." + smpName + ".inactive", false);
            boolean isInactive = (currentTime - lastActivity) > inactivityDuration;

            // Update inactive status if changed
            if (isInactive != wasInactive) {
                database.set("smps." + smpName + ".inactive", isInactive);
                if (isInactive) {
                    // Broadcast message about SMP becoming inactive
                    String displayName = database.getString("smps." + smpName + ".display_name", smpName);
                    String message = colorize(config.getString("messages.smp_inactive", "&eSMP &a%name% &eis now inactive and fully editable."))
                            .replace("%name%", displayName);
                    Bukkit.broadcastMessage(message);
                    getLogger().info("SMP " + smpName + " marked as inactive");
                }
            }

            // Check for specific inactivity thresholds
            if (isInactive) {
                // Calculate days of inactivity
                long inactivityMillis = currentTime - lastActivity;
                int inactivityDays = (int) (inactivityMillis / (24 * 60 * 60 * 1000L));

                // Check each threshold
                for (Integer threshold : inactivityThresholds) {
                    // Check if this threshold has been reached and not yet notified
                    if (inactivityDays >= threshold && !isInactivityThresholdNotified(smpName, threshold)) {
                        // Get the display name for the SMP
                        String displayName = database.getString("smps." + smpName + ".display_name", smpName);

                        // Execute the velocity command
                        executeVelocityInactivityCommand(smpName, displayName, threshold);

                        // Mark this threshold as notified
                        markInactivityThresholdNotified(smpName, threshold);

                        getLogger().info("Sent inactivity notification for SMP " + smpName + " at " + threshold + " days");
                    }
                }
            }

            // Check for auto-deletion
            if (isInactive && (currentTime - lastActivity) > autoDeletionMillis) {
                String displayName = database.getString("smps." + smpName + ".display_name", smpName);
                String message = colorize(config.getString("messages.smp_auto_deleted", "&cSMP &e%name% &chas been automatically deleted due to inactivity."))
                        .replace("%name%", displayName);
                Bukkit.broadcastMessage(message);
                getLogger().info("Auto-deleting inactive SMP: " + smpName);

                // Delete the SMP
                deleteSMP(smpName);
            }
        }

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * Updates the activity timestamp for an SMP
     * @param smpName The name of the SMP to update
     */
    public void updateSMPActivity(String smpName) {
        if (database.contains("smps." + smpName)) {
            database.set("smps." + smpName + ".last_activity", System.currentTimeMillis());
            database.set("smps." + smpName + ".inactive", false);

            // Reset all inactivity notifications when the SMP becomes active again
            if (database.contains("smps." + smpName + ".inactivity_notifications")) {
                database.set("smps." + smpName + ".inactivity_notifications", null);
                getLogger().info("Reset inactivity notifications for SMP: " + smpName);
            }

            try {
                database.save(databaseFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * Checks if an SMP is inactive
     * @param smpName The name of the SMP to check
     * @return true if the SMP is inactive, false otherwise
     */
    public boolean isSMPInactive(String smpName) {
        return database.getBoolean("smps." + smpName + ".inactive", false);
    }

    /**
     * Starts the average player count tracker task
     */
    private void startAveragePlayerTracker() {
        int checkInterval = config.getInt("average_players.check_interval", 600); // Default: 10 minutes (600 seconds)
        new BukkitRunnable() {
            @Override
            public void run() {
                updateAveragePlayerCounts();
            }
        }.runTaskTimer(this, 20L, checkInterval * 20L); // Convert seconds to ticks
        getLogger().info("Started average player count tracker (interval: " + checkInterval + " seconds)");
    }

    /**
     * Starts the daily average player task checker
     */
    private void startDailyAveragePlayerTask() {
        if (!config.getBoolean("average_players.daily_task.enabled", true)) {
            getLogger().info("Daily average player task is disabled in config");
            return;
        }

        // Initialize next task time if not set
        if (!database.contains("next_task")) {
            updateNextTaskTime();
        }

        // Start a repeating task that checks every 10 minutes if it's time to run the daily task
        new BukkitRunnable() {
            @Override
            public void run() {
                checkDailyTaskExecution();
            }
        }.runTaskTimer(this, 20L, 10 * 60 * 20L); // Check every 10 minutes (10 * 60 seconds * 20 ticks/second)

        getLogger().info("Started daily average player task checker (checks every 10 minutes)");
    }

    /**
     * Checks if it's time to execute the daily task and runs it if needed
     */
    private void checkDailyTaskExecution() {
        if (!config.getBoolean("average_players.daily_task.enabled", true)) {
            return;
        }

        long nextTaskTime = database.getLong("next_task", 0);
        long currentTime = System.currentTimeMillis();

        // If it's time to run the task
        if (currentTime >= nextTaskTime) {
            debugLog("It's time to run the daily average player task");
            runDailyAveragePlayerTask();
            updateNextTaskTime();
        } else {
            debugLog("Not yet time to run daily average player task. Next run in " +
                    ((nextTaskTime - currentTime) / 1000 / 60) + " minutes");
        }
    }

    /**
     * Updates the next task time in the database
     */
    private void updateNextTaskTime() {
        // Get configured hour and minute
        int hour = config.getInt("average_players.daily_task.hour", 3);
        int minute = config.getInt("average_players.daily_task.minute", 0);

        // Calculate the next task time
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // If the calculated time is in the past, add one day
        if (calendar.getTimeInMillis() <= System.currentTimeMillis()) {
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }

        long nextTaskTime = calendar.getTimeInMillis();
        database.set("next_task", nextTaskTime);

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        getLogger().info("Next daily average player task scheduled for: " + sdf.format(new Date(nextTaskTime)));
    }

    /**
     * Updates the average player counts for all SMPs
     */
    private void updateAveragePlayerCounts() {
        ConfigurationSection smps = database.getConfigurationSection("smps");
        if (smps == null) return;

        String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        for (String smpName : smps.getKeys(false)) {
            // Count current players in this SMP
            int currentPlayers = countSMPPlayers(smpName);

            // Get or initialize the tracking data for today
            int totalPlayersSeen = database.getInt("smps." + smpName + ".average_players." + currentDate + ".total_players_seen", 0);
            int checks = database.getInt("smps." + smpName + ".average_players." + currentDate + ".checks", 0);

            // Update the tracking data
            totalPlayersSeen += currentPlayers;
            checks++;

            // Save the updated data
            database.set("smps." + smpName + ".average_players." + currentDate + ".total_players_seen", totalPlayersSeen);
            database.set("smps." + smpName + ".average_players." + currentDate + ".checks", checks);

            debugLog("Updated average player count for SMP " + smpName + ": " + currentPlayers +
                    " players, total: " + totalPlayersSeen + ", checks: " + checks);
        }

        try {
            database.save(databaseFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * Tracks a unique player join for an SMP
     * @param smpName The name of the SMP
     * @param playerUUID The UUID of the player
     */
    private void trackUniquePlayerJoin(String smpName, UUID playerUUID) {
        // Get the current list of unique players
        List<String> uniquePlayers = database.getStringList("smps." + smpName + ".unique_players");
        String uuidString = playerUUID.toString();

        // Add the player if not already in the list
        if (!uniquePlayers.contains(uuidString)) {
            uniquePlayers.add(uuidString);
            database.set("smps." + smpName + ".unique_players", uniquePlayers);

            try {
                database.save(databaseFile);
            } catch (IOException e) {
                e.printStackTrace();
            }

            debugLog("Added player " + playerUUID + " to unique players list for SMP " + smpName +
                    ". Total unique players: " + uniquePlayers.size());
        }
    }

    /**
     * Gets the average player count for an SMP on a specific date
     * @param smpName The name of the SMP
     * @param date The date in format yyyy-MM-dd
     * @return The average player count, or 0 if no data exists
     */
    public double getAveragePlayerCount(String smpName, String date) {
        String path = "smps." + smpName + ".average_players." + date;

        if (!database.contains(path)) {
            return 0.0;
        }

        int totalPlayersSeen = database.getInt(path + ".total_players_seen", 0);
        int checks = database.getInt(path + ".checks", 0);

        if (checks == 0) {
            return 0.0;
        }

        return (double) totalPlayersSeen / checks;
    }

    /**
     * Gets the average player count for an SMP on the current date
     * @param smpName The name of the SMP
     * @return The average player count, or 0 if no data exists
     */
    public double getCurrentAveragePlayerCount(String smpName) {
        String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        return getAveragePlayerCount(smpName, currentDate);
    }

    /**
     * Gets the average player count for an SMP over the last week
     * @param smpName The name of the SMP
     * @return The average player count over the last week, or 0 if no data exists
     */
    public double getWeeklyAveragePlayerCount(String smpName) {
        int totalPlayersSeen = 0;
        int totalChecks = 0;

        // Get dates for the last 7 days (or less if the SMP is newer)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();

        // Start with today and go back 6 days (7 days total)
        for (int i = 0; i < 7; i++) {
            String date = sdf.format(calendar.getTime());
            String path = "smps." + smpName + ".average_players." + date;

            if (database.contains(path)) {
                totalPlayersSeen += database.getInt(path + ".total_players_seen", 0);
                totalChecks += database.getInt(path + ".checks", 0);
            }

            // Go back one day
            calendar.add(Calendar.DAY_OF_YEAR, -1);
        }

        if (totalChecks == 0) {
            return 0.0;
        }

        return (double) totalPlayersSeen / totalChecks;
    }

    /**
     * Runs the daily average player task
     */
    private void runDailyAveragePlayerTask() {
        getLogger().info("Running daily average player task...");

        ConfigurationSection smps = database.getConfigurationSection("smps");
        if (smps == null) {
            getLogger().info("No SMPs found in database");
            return;
        }

        // Get minimum SMP age in hours
        int minSMPAgeHours = config.getInt("average_players.daily_task.min_smp_age_hours", 48);
        long minSMPAgeMillis = minSMPAgeHours * 60 * 60 * 1000L; // Convert hours to milliseconds
        long currentTime = System.currentTimeMillis();

        // Process each SMP
        for (String smpName : smps.getKeys(false)) {
            // Check if SMP is old enough
            long creationTime = database.getLong("smps." + smpName + ".creation_time", 0);
            if (currentTime - creationTime < minSMPAgeMillis) {
                debugLog("Skipping SMP " + smpName + " as it's not old enough");
                continue;
            }

            // Calculate the current average player count over the last week
            double averagePlayerCount = getWeeklyAveragePlayerCount(smpName);

            // Send plugin message to Velocity
            sendAveragePlayerCountToVelocity(smpName, averagePlayerCount);
            getLogger().info("Sent average player count for SMP " + smpName + ": " + String.format("%.2f", averagePlayerCount));

            // Clean up old average player data
            cleanupOldAveragePlayerData(smpName);
        }

        // Next task time will be updated by the checker
    }

    /**
     * Sends the average player count to Velocity via plugin messaging
     * @param smpName The name of the SMP
     * @param averagePlayerCount The average player count
     */
    private void sendAveragePlayerCountToVelocity(String smpName, double averagePlayerCount) {
        // Find an online player to send the message through
        if (Bukkit.getOnlinePlayers().isEmpty()) {
            getLogger().warning("Cannot send average player count via plugin messaging: no players online");
            getLogger().info("Falling back to console command for average player count");

            // Fall back to console command as a backup method
            String command = "velocitysmp average " + smpName + " " + String.format("%.2f", averagePlayerCount);
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
            getLogger().info("Executed command: /" + command);
            return;
        }

        Player player = Bukkit.getOnlinePlayers().iterator().next();

        // Create the message
        ByteArrayDataOutput out = ByteStreams.newDataOutput();
        out.writeUTF("Average"); // Sub-channel
        out.writeUTF(smpName);
        out.writeUTF(String.format("%.2f", averagePlayerCount));

        // Send the message
        player.sendPluginMessage(this, PLUGIN_CHANNEL, out.toByteArray());
        getLogger().info("Sent average player count via plugin messaging");
    }

    /**
     * Cleans up old average player data for an SMP
     * @param smpName The name of the SMP
     */
    private void cleanupOldAveragePlayerData(String smpName) {
        int retentionDays = config.getInt("average_players.daily_task.retention_days", 7);

        // Get all dates for this SMP
        ConfigurationSection averagePlayersSection = database.getConfigurationSection("smps." + smpName + ".average_players");
        if (averagePlayersSection == null) {
            return;
        }

        // Get dates to keep
        Set<String> datesToKeep = new HashSet<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();

        // Start with today and go back retention_days - 1 days
        for (int i = 0; i < retentionDays; i++) {
            datesToKeep.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_YEAR, -1);
        }

        // Remove dates that are not in the keep list
        for (String date : averagePlayersSection.getKeys(false)) {
            if (!datesToKeep.contains(date)) {
                database.set("smps." + smpName + ".average_players." + date, null);
                debugLog("Removed old average player data for SMP " + smpName + " on date " + date);
            }
        }
    }

    /**
     * Checks if a specific inactivity threshold has already been notified for an SMP
     * @param smpName The name of the SMP to check
     * @param threshold The inactivity threshold in days
     * @return true if the threshold has been notified, false otherwise
     */
    private boolean isInactivityThresholdNotified(String smpName, int threshold) {
        return database.getBoolean("smps." + smpName + ".inactivity_notifications." + threshold, false);
    }

    /**
     * Marks a specific inactivity threshold as notified for an SMP
     * @param smpName The name of the SMP to mark
     * @param threshold The inactivity threshold in days
     */
    private void markInactivityThresholdNotified(String smpName, int threshold) {
        database.set("smps." + smpName + ".inactivity_notifications." + threshold, true);
    }

    /**
     * Sends a plugin message to Velocity to notify about SMP inactivity
     * @param smpName The internal name of the SMP
     * @param displayName The display name of the SMP
     * @param days The number of days of inactivity
     */
    private void executeVelocityInactivityCommand(String smpName, String displayName, int days) {
        // Find an online player to send the message through
        if (Bukkit.getOnlinePlayers().isEmpty()) {
            getLogger().warning("Cannot send inactivity notification via plugin messaging: no players online");
            getLogger().info("Falling back to console command for inactivity notification");

            // Fall back to console command as a backup method
            String command = "velocitysmp inactivity " + smpName + " " + days;
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
            getLogger().info("Executed command: /" + command);
            return;
        }

        Player player = Bukkit.getOnlinePlayers().iterator().next();

        // Create the message
        ByteArrayDataOutput out = ByteStreams.newDataOutput();
        out.writeUTF("Inactivity"); // Sub-channel
        out.writeUTF(smpName);
        out.writeUTF(String.valueOf(days));

        // Send the message
        player.sendPluginMessage(this, PLUGIN_CHANNEL, out.toByteArray());

        // Log the notification
        String message = colorize(config.getString("messages.smp_inactivity_notification", "&eSMP &a%name% &ehas been inactive for &c%days% &edays."))
                .replace("%name%", displayName)
                .replace("%days%", String.valueOf(days));
        getLogger().info(message);
        getLogger().info("Sent inactivity notification via plugin messaging");
    }

    /**
     * Sends a plugin message to Velocity for SMP creation
     * @param player The player creating the SMP
     * @param smpName The name of the SMP
     * @param privacy The privacy setting (public/private)
     * @param difficulty The difficulty setting
     */
    public void executeVelocitySMPCreateCommand(Player player, String smpName, String privacy, String difficulty) {
        // Create the message
        ByteArrayDataOutput out = ByteStreams.newDataOutput();
        out.writeUTF("Create"); // Sub-channel
        out.writeUTF(player.getName());
        out.writeUTF(smpName);
        out.writeUTF(privacy);
        out.writeUTF(difficulty);

        // Send the message
        player.sendPluginMessage(this, PLUGIN_CHANNEL, out.toByteArray());

        // Log the message sending
        getLogger().info("Sent SMP creation request via plugin messaging for player " + player.getName() + ", SMP: " + smpName);
    }

    /**
     * Sends a plugin message to Velocity to notify about SMP deletion
     * @param smpName The internal name of the SMP to be deleted
     */
    private void executeVelocityDeleteCommand(String smpName) {
        // Get the display name for the SMP
        String displayName = database.getString("smps." + smpName + ".display_name", smpName);

        // Find an online player to send the message through
        if (Bukkit.getOnlinePlayers().isEmpty()) {
            getLogger().warning("Cannot send deletion notification via plugin messaging: no players online");
            getLogger().info("Falling back to console command for deletion notification");

            // Fall back to console command as a backup method
            String command = "velocitysmp delete " + smpName;
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
            getLogger().info("Executed command: /" + command);
            return;
        }

        Player player = Bukkit.getOnlinePlayers().iterator().next();

        // Create the message
        ByteArrayDataOutput out = ByteStreams.newDataOutput();
        out.writeUTF("Delete"); // Sub-channel
        out.writeUTF(smpName);

        // Send the message
        player.sendPluginMessage(this, PLUGIN_CHANNEL, out.toByteArray());

        // Log the notification
        String message = colorize(config.getString("messages.smp_deleted_notification", "&cSMP &e%name% &chas been deleted."))
                .replace("%name%", displayName);
        getLogger().info(message);
        getLogger().info("Sent deletion notification via plugin messaging");
    }

    /**
     * Deletes an SMP programmatically (used for auto-deletion)
     * @param smpName The name of the SMP to delete
     */
    private void deleteSMP(String smpName) {
        MVWorldManager worldManager = mvCore.getMVWorldManager();
        String[] worldTypes = {"", "_nether", "_the_end"};

        // Teleport all players out of the SMP worlds
        List<Player> playersToTeleport = new ArrayList<>();
        for (String type : worldTypes) {
            World world = Bukkit.getWorld(generateWorldName(smpName, type));
            if (world != null) {
                playersToTeleport.addAll(world.getPlayers());
            }
        }

        // Teleport all collected players
        for (Player p : playersToTeleport) {
            if (p != null && p.isOnline()) { // Double check player state
                p.performCommand("spawn"); // Use EssentialsX spawn command
                String displayName = database.getString("smps." + smpName + ".display_name", smpName);
                sendMessage(p, "smp_world_deleted_teleporting", "%name%", displayName); // Inform the player
            }
        }

        // Delete MV inventory group
        new BukkitRunnable() {
            @Override
            public void run() {
                // Delete worlds using MV API
                for (String type : worldTypes) {
                    String worldName = generateWorldName(smpName, type);
                    if (worldManager.isMVWorld(worldName)) {
                        worldManager.deleteWorld(worldName, true, true); // true, true = delete files and remove from config
                    }
                }

                // Manually delete Multiverse-Inventories files
                File mvInvDir = new File(getDataFolder().getParentFile(), "Multiverse-Inventories");

                // Delete group folder
                File groupDir = new File(mvInvDir, "groups/smp_" + smpName);
                if (groupDir.exists()) {
                    deleteDirectory(groupDir);
                }

                // Delete world inventory files
                for (String type : worldTypes) {
                    String worldName = generateWorldName(smpName, type);
                    File worldDir = new File(mvInvDir, "worlds/" + worldName);
                    if (worldDir.exists()) {
                        deleteDirectory(worldDir);
                    }
                }

                ConfigurationSection locationsSection = database.getConfigurationSection("locations");
                if (locationsSection != null) {
                    for (String playerUUID : locationsSection.getKeys(false)) {
                        // Remove this SMP's location data for each player
                        database.set("locations." + playerUUID + "." + smpName, null);

                        // If player has no more locations, remove the player entry
                        if (database.getConfigurationSection("locations." + playerUUID) != null &&
                                database.getConfigurationSection("locations." + playerUUID).getKeys(false).isEmpty()) {
                            database.set("locations." + playerUUID + "." + smpName, null);
                        }
                    }
                }
                // Remove from database
                database.set("smps." + smpName, null);
                try {
                    database.save(databaseFile);
                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }
                pluginManager.cleanupSMP(smpName);

                // Cleanup economy
                multiworldMoneyManager.cleanupSMPEconomy(smpName);

                // Execute the velocity delete command
                executeVelocityDeleteCommand(smpName);
            }
        }.runTaskLater(this, 20L); // 1 second delay to ensure MV inventory deletion completes
    }

    @Override
    public void onDisable() {
        // Clear any active inventories
        Bukkit.getOnlinePlayers().forEach(player -> {
            if(player.getOpenInventory().getTitle().equals(config.getString("gui.smp_join.title")) ||
                    player.getOpenInventory().getTitle().equals(config.getString("gui.smp_creator.title")) ||
                    player.getOpenInventory().getTitle().matches(config.getString("gui.smp_settings.title").replace("%name%", ".*"))) {
                player.closeInventory();
            }
        });

        // Unregister plugin messaging channels
        getServer().getMessenger().unregisterOutgoingPluginChannel(this, PLUGIN_CHANNEL);
        getServer().getMessenger().unregisterIncomingPluginChannel(this, PLUGIN_CHANNEL, this);
        getLogger().info("Unregistered plugin messaging channels");
    }
    private String formatTime(long millis) {
        long totalSeconds = TimeUnit.MILLISECONDS.toSeconds(millis);
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;

        StringBuilder sb = new StringBuilder();
        if (minutes > 0) {
            sb.append(minutes).append(" minute").append(minutes > 1 ? "s" : "");
            if (seconds > 0) {
                sb.append(" ");
            }
        }
        if (seconds > 0 || minutes == 0) { // Show seconds if non-zero or if minutes is zero
            sb.append(seconds).append(" second").append(seconds != 1 ? "s" : "");
        }
        return sb.toString();
    }

    /**
     * Logs debug information if debug is enabled in config
     * @param message The message to log
     */
    private void debugLog(String message) {
        if (config.getBoolean("debug_logging", false)) {
            getLogger().info("[DEBUG] " + message);
        }
    }

    /**
     * Checks if a command is on cooldown for a player
     * @param player The player to check
     * @param command The command to check (without slash)
     * @return true if the player can use the command, false if on cooldown
     */
    public boolean checkCommandCooldown(Player player, String command) {
        if (player.hasPermission("smp.cooldown.bypass")) {
            debugLog("Player " + player.getName() + " bypassed cooldown for command: " + command);
            return true; // Player can bypass cooldowns
        }

        // Get the cooldown configuration
        ConfigurationSection cooldownsSection = config.getConfigurationSection("command_cooldowns");
        if (cooldownsSection == null || !cooldownsSection.contains(command)) {
            debugLog("No cooldown configured for command: " + command);
            return true; // No cooldown for this command
        }

        long cooldownSeconds = cooldownsSection.getLong(command);
        if (cooldownSeconds <= 0) {
            debugLog("Cooldown for command " + command + " is set to 0 or negative: " + cooldownSeconds);
            return true; // No cooldown set
        }

        // Get the command's cooldown map, creating if needed
        Map<UUID, Long> commandMap = commandCooldownTimestamps.computeIfAbsent(command, k -> new HashMap<>());

        // Check if player has used this command before
        long lastUsed = commandMap.getOrDefault(player.getUniqueId(), 0L);
        if (lastUsed == 0) {
            debugLog("Player " + player.getName() + " using command " + command + " for the first time");
            return true; // First time using command
        }

        // Check if cooldown has expired
        long currentTime = System.currentTimeMillis();
        long cooldownEndTime = lastUsed + (cooldownSeconds * 1000);

        if (currentTime < cooldownEndTime) {
            // Still on cooldown
            long remainingMillis = cooldownEndTime - currentTime;
            debugLog("Player " + player.getName() + " tried to use command " + command + " but has " +
                    formatTime(remainingMillis) + " cooldown remaining");
            sendMessage(player, "command_cooldown_active", "%command%", command, "%time%", formatTime(remainingMillis));
            return false;
        }

        debugLog("Player " + player.getName() + " cooldown for command " + command + " has expired");
        return true; // Cooldown expired
    }

    /**
     * Records that a player has used a command for cooldown tracking
     * @param player The player who used the command
     * @param command The command that was used (without slash)
     */
    public void recordCommandUse(Player player, String command) {
        // Get the cooldown configuration
        ConfigurationSection cooldownsSection = config.getConfigurationSection("command_cooldowns");
        if (cooldownsSection == null || !cooldownsSection.contains(command)) {
            debugLog("No cooldown to record for command: " + command);
            return; // No cooldown for this command
        }

        // Get the command's cooldown map, creating if needed
        Map<UUID, Long> commandMap = commandCooldownTimestamps.computeIfAbsent(command, k -> new HashMap<>());

        // Record the current time
        commandMap.put(player.getUniqueId(), System.currentTimeMillis());
        debugLog("Recorded command use for player " + player.getName() + ": " + command +
                " with cooldown of " + cooldownsSection.getLong(command) + " seconds");
    }
    public SMPPluginGUI getPluginGUI() {
        return pluginGUI;
    }

    public File getDatabaseFile() {
        return databaseFile;
    }

    public MultiworldMoneyManager getMultiworldMoneyManager() {
        return multiworldMoneyManager;
    }

    public SMPPluginManager getPluginManager() {
        return pluginManager;
    }

    /**
     * Disables achievement announcements for all existing SMP worlds
     * This is called during plugin startup to ensure existing SMPs have the gamerule set
     */
    private void disableAchievementAnnouncementsForExistingSMPs() {
        ConfigurationSection smps = database.getConfigurationSection("smps");
        if (smps == null) {
            return;
        }

        int worldsUpdated = 0;
        String[] worldTypes = {"", "_nether", "_the_end"};

        for (String smpName : smps.getKeys(false)) {
            for (String type : worldTypes) {
                String worldName = generateWorldName(smpName, type);
                World world = Bukkit.getWorld(worldName);
                if (world != null) {
                    // Check if the gamerule is already set correctly
                    Boolean currentValue = world.getGameRuleValue(GameRule.ANNOUNCE_ADVANCEMENTS);
                    if (currentValue == null || currentValue) {
                        // Set the gamerule to false
                        world.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
                        worldsUpdated++;
                        debugLog("Disabled achievement announcements for existing world: " + worldName);
                    }
                }
            }
        }

        if (worldsUpdated > 0) {
            getLogger().info("Disabled achievement announcements for " + worldsUpdated + " existing SMP worlds");
        } else {
            getLogger().info("All existing SMP worlds already have achievement announcements disabled");
        }
    }


}
